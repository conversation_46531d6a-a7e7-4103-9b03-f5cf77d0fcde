"""
Setup script for Business Data Collector CLI
"""
from setuptools import setup, find_packages

setup(
    name="business-data-collector",
    version="1.0.0",
    description="CLI tool for collecting business data and scraping contact information",
    author="Business Data Collector Team",
    packages=find_packages(),
    install_requires=[
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0",
        "pandas>=2.0.0",
        "python-dotenv>=1.0.0",
        "lxml>=4.9.0",
        "crawl4ai>=0.3.0",
        "typer>=0.9.0",
        "rich>=13.0.0",
        "geopy>=2.4.0",
        "questionary>=2.0.0",
    ],
    entry_points={
        "console_scripts": [
            "business-cli=cli.main:cli_main",
        ],
    },
    python_requires=">=3.8",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
)
