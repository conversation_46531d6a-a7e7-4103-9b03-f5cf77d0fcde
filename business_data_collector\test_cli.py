"""
Test script for the Business Data Collector CLI
"""
import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd):
    """Run a CLI command and return the result"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=30
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"

def test_cli_basic():
    """Test basic CLI functionality"""
    print("🧪 Testing Business Data Collector CLI")
    print("=" * 50)
    
    # Test 1: CLI help
    print("1. Testing CLI help...")
    success, stdout, stderr = run_command("python -m cli.main --help")
    if success and "Business Data Collector CLI" in stdout:
        print("   ✅ CLI help works")
    else:
        print("   ❌ CLI help failed")
        print(f"   Error: {stderr}")
    
    # Test 2: Categories list
    print("2. Testing categories list...")
    success, stdout, stderr = run_command("python -m cli.main categories list")
    if success and "Food & Dining" in stdout:
        print("   ✅ Categories list works")
    else:
        print("   ❌ Categories list failed")
        print(f"   Error: {stderr}")
    
    # Test 3: Categories search
    print("3. Testing categories search...")
    success, stdout, stderr = run_command("python -m cli.main categories search restaurant")
    if success and "Restaurants" in stdout:
        print("   ✅ Categories search works")
    else:
        print("   ❌ Categories search failed")
        print(f"   Error: {stderr}")
    
    # Test 4: Category info
    print("4. Testing category info...")
    success, stdout, stderr = run_command("python -m cli.main categories info restaurants")
    if success and "Full-service restaurants" in stdout:
        print("   ✅ Category info works")
    else:
        print("   ❌ Category info failed")
        print(f"   Error: {stderr}")
    
    # Test 5: Config show
    print("5. Testing config show...")
    success, stdout, stderr = run_command("python -m cli.main config show")
    if success and "default_radius" in stdout:
        print("   ✅ Config show works")
    else:
        print("   ❌ Config show failed")
        print(f"   Error: {stderr}")
    
    # Test 6: Results list
    print("6. Testing results list...")
    success, stdout, stderr = run_command("python -m cli.main results list")
    if success:  # May have no results, which is fine
        print("   ✅ Results list works")
    else:
        print("   ❌ Results list failed")
        print(f"   Error: {stderr}")
    
    # Test 7: Scrape help (check if scraping is available)
    print("7. Testing scrape help...")
    success, stdout, stderr = run_command("python -m cli.main scrape --help")
    if success and "Scrape emails and contact info" in stdout:
        print("   ✅ Scrape help works")
    else:
        print("   ❌ Scrape help failed")
        print(f"   Error: {stderr}")
    
    print("\n" + "=" * 50)
    print("✅ Basic CLI tests completed!")

def test_cli_integration():
    """Test CLI integration with a small collection"""
    print("\n🔬 Testing CLI Integration")
    print("=" * 50)
    
    # Test small collection (only if we have existing data)
    output_dir = Path("output")
    if output_dir.exists() and list(output_dir.glob("*.csv")):
        print("1. Testing small data collection...")
        success, stdout, stderr = run_command(
            'python -m cli.main collect quick -c restaurants -l "Pittsburgh, PA" -r 1 -s overpass'
        )
        if success:
            print("   ✅ Small collection works")
        else:
            print("   ❌ Small collection failed")
            print(f"   Error: {stderr}")
        
        # Test email scraping on existing data
        csv_files = list(output_dir.glob("*.csv"))
        if csv_files:
            print("2. Testing email scraping...")
            csv_file = csv_files[0]
            success, stdout, stderr = run_command(
                f'python -m cli.main scrape emails "{csv_file}" -a 5 -b 2 -c 1'
            )
            if success:
                print("   ✅ Email scraping works")
            else:
                print("   ❌ Email scraping failed")
                print(f"   Error: {stderr}")
    else:
        print("1. Skipping integration tests (no existing data)")
    
    print("\n" + "=" * 50)
    print("✅ Integration tests completed!")

def test_cli_error_handling():
    """Test CLI error handling"""
    print("\n🚨 Testing CLI Error Handling")
    print("=" * 50)
    
    # Test 1: Invalid category
    print("1. Testing invalid category...")
    success, stdout, stderr = run_command(
        'python -m cli.main collect quick -c invalid_category -l "Pittsburgh, PA"'
    )
    if not success and "Invalid categories" in stderr:
        print("   ✅ Invalid category error handling works")
    else:
        print("   ❌ Invalid category error handling failed")
    
    # Test 2: Invalid location
    print("2. Testing invalid location...")
    success, stdout, stderr = run_command(
        'python -m cli.main collect quick -c restaurants -l "NonexistentCity12345"'
    )
    if not success:
        print("   ✅ Invalid location error handling works")
    else:
        print("   ❌ Invalid location error handling failed")
    
    # Test 3: Missing required arguments
    print("3. Testing missing arguments...")
    success, stdout, stderr = run_command(
        'python -m cli.main collect quick'
    )
    if not success:
        print("   ✅ Missing arguments error handling works")
    else:
        print("   ❌ Missing arguments error handling failed")
    
    print("\n" + "=" * 50)
    print("✅ Error handling tests completed!")

def main():
    """Run all CLI tests"""
    print("🎯 Business Data Collector CLI Test Suite")
    print("=" * 60)
    
    # Change to the correct directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        test_cli_basic()
        test_cli_integration()
        test_cli_error_handling()
        
        print("\n🎉 All CLI tests completed successfully!")
        print("The Business Data Collector CLI is ready for use!")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
