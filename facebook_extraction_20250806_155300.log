2025-08-06 15:53:00,404 - __main__ - INFO - Starting Facebook email extraction...
2025-08-06 15:53:00,404 - __main__ - INFO - Input file: carminstest_contacts_20250806_150823.csv
2025-08-06 15:53:00,405 - __main__ - INFO - Output file: carminstest_contacts_20250806_150823_with_facebook_emails.csv
2025-08-06 15:53:00,405 - __main__ - INFO - Loading business data from carminstest_contacts_20250806_150823.csv
2025-08-06 15:53:00,411 - __main__ - INFO - Found 7 businesses with Facebook pages
2025-08-06 15:53:01,021 - collectors.apify_facebook - INFO - Using official Apify client
2025-08-06 15:53:01,022 - __main__ - INFO - Processing batch 1: 5 URLs
2025-08-06 15:53:01,022 - collectors.apify_facebook - INFO - Starting Apify actor run for 5 URLs
2025-08-06 15:53:01,023 - apify_client - DEBUG - Sending request
2025-08-06 15:53:01,024 - httpcore.connection - DEBUG - connect_tcp.started host='api.apify.com' port=443 local_address=None timeout=360 socket_options=None
2025-08-06 15:53:01,074 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E58BF230>
2025-08-06 15:53:01,075 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000241E59087A0> server_hostname='api.apify.com' timeout=360
2025-08-06 15:53:01,133 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E5883110>
2025-08-06 15:53:01,133 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-06 15:53:01,134 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:01,134 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-06 15:53:01,135 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:01,135 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-06 15:53:01,383 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Wed, 06 Aug 2025 19:53:01 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, POST'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'200'), (b'Location', b'https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:01,384 - httpx - INFO - HTTP Request: POST https://api.apify.com/v2/acts/Catqz8yCm9MEuNd8x/runs "HTTP/1.1 201 Created"
2025-08-06 15:53:01,385 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-06 15:53:01,386 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:01,387 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:01,387 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:01,388 - apify_client - DEBUG - Request successful
2025-08-06 15:53:01,389 - apify_client - DEBUG - Sending request
2025-08-06 15:53:01,389 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,390 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:01,390 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,390 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:01,391 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,444 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:01 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:01,444 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:01,445 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,445 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:01,445 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:01,445 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:01,446 - apify_client - DEBUG - Request successful
2025-08-06 15:53:01,446 - apify_client - DEBUG - Sending request
2025-08-06 15:53:01,446 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,447 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:01,447 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,447 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:01,447 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,526 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:01 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'ETag', b'W/"baf-I99UCkYkLy9exNfPDUSgYspd03g"'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:01,526 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/acts/Catqz8yCm9MEuNd8x "HTTP/1.1 200 OK"
2025-08-06 15:53:01,527 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,530 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:01,530 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:01,530 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:01,530 - apify_client - DEBUG - Request successful
2025-08-06 15:53:01,531 - apify_client - DEBUG - Sending request
2025-08-06 15:53:01,531 - apify_client - DEBUG - Sending request
2025-08-06 15:53:01,532 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,532 - httpcore.connection - DEBUG - connect_tcp.started host='api.apify.com' port=443 local_address=None timeout=360 socket_options=None
2025-08-06 15:53:01,533 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:01,533 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,534 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:01,534 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,568 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E5921450>
2025-08-06 15:53:01,568 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000241E59087A0> server_hostname='api.apify.com' timeout=360
2025-08-06 15:53:01,586 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:01 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:01,587 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:01,587 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,587 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:01,588 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:01,588 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:01,588 - apify_client - DEBUG - Request successful
2025-08-06 15:53:01,643 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E58C2060>
2025-08-06 15:53:01,644 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,645 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:01,645 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,645 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:01,646 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,727 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:02 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:01,728 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:01,729 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,729 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:01,730 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:01,730 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:01,730 - apify_client - DEBUG - Request successful
2025-08-06 15:53:01,731 - apify_client - DEBUG - Sending request
2025-08-06 15:53:01,731 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,732 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:01,732 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,732 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:01,733 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,806 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:02 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'ETag', b'W/"baf-I99UCkYkLy9exNfPDUSgYspd03g"'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:01,808 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/acts/Catqz8yCm9MEuNd8x "HTTP/1.1 200 OK"
2025-08-06 15:53:01,809 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,811 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:01,811 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:01,812 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:01,812 - apify_client - DEBUG - Request successful
2025-08-06 15:53:01,814 - apify_client - DEBUG - Sending request
2025-08-06 15:53:01,814 - apify_client - DEBUG - Sending request
2025-08-06 15:53:01,816 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,816 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,816 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:01,817 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:01,817 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,818 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:01,818 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:01,818 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:01,819 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,819 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:01,891 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:02 GMT'), (b'Content-Type', b'text/plain; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-transform'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Content-Disposition', b'inline; filename="Xl8UeVTn2ngBv6Vg1.log"')])
2025-08-06 15:53:01,893 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1/log?stream=1&raw=1 "HTTP/1.1 200 OK"
2025-08-06 15:53:01,894 - apify_client - DEBUG - Request successful
2025-08-06 15:53:01,894 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:02,589 - apify_client - DEBUG - Sending request
2025-08-06 15:53:02,590 - httpcore.connection - DEBUG - connect_tcp.started host='api.apify.com' port=443 local_address=None timeout=360 socket_options=None
2025-08-06 15:53:02,641 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E5948050>
2025-08-06 15:53:02,642 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000241E59087A0> server_hostname='api.apify.com' timeout=360
2025-08-06 15:53:02,716 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E59345F0>
2025-08-06 15:53:02,717 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:02,719 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:02,719 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:02,720 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:02,721 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:02,801 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:03 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:02,802 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:02,802 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:02,803 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:02,803 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:02,803 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:02,804 - apify_client - DEBUG - Request successful
2025-08-06 15:53:02,975 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:03 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:02,976 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1?waitForFinish=999999 "HTTP/1.1 200 OK"
2025-08-06 15:53:02,976 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:03,805 - apify_client - DEBUG - Sending request
2025-08-06 15:53:03,806 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:03,808 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:03,808 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:03,809 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:03,809 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:04,025 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:04 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:04,026 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:04,026 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:04,027 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:04,027 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:04,027 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:04,027 - apify_client - DEBUG - Request successful
2025-08-06 15:53:05,028 - apify_client - DEBUG - Sending request
2025-08-06 15:53:05,029 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:05,029 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:05,030 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:05,030 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:05,030 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:05,260 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:05 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:05,261 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:05,262 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:05,262 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:05,263 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:05,263 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:05,263 - apify_client - DEBUG - Request successful
2025-08-06 15:53:06,265 - apify_client - DEBUG - Sending request
2025-08-06 15:53:06,266 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:06,267 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:06,268 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:06,269 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:06,269 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:06,350 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:06 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:06,351 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:06,352 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:06,353 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:06,353 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:06,354 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:06,354 - apify_client - DEBUG - Request successful
2025-08-06 15:53:07,356 - apify_client - DEBUG - Sending request
2025-08-06 15:53:07,356 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:07,358 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:07,358 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:07,359 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:07,359 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:07,426 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:07 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:07,427 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:07,427 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:07,428 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:07,428 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:07,428 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:07,428 - apify_client - DEBUG - Request successful
2025-08-06 15:53:08,430 - apify_client - DEBUG - Sending request
2025-08-06 15:53:08,430 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:08,431 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:08,431 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:08,431 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:08,431 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:08,481 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:08 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:08,482 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:08,482 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:08,482 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:08,483 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:08,483 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:08,483 - apify_client - DEBUG - Request successful
2025-08-06 15:53:09,484 - apify_client - DEBUG - Sending request
2025-08-06 15:53:09,485 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:09,485 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:09,485 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:09,485 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:09,486 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:09,545 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:09 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:09,546 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:09,546 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:09,547 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:09,548 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:09,548 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:09,548 - apify_client - DEBUG - Request successful
2025-08-06 15:53:10,549 - apify_client - DEBUG - Sending request
2025-08-06 15:53:10,550 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:10,550 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:10,550 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:10,550 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:10,551 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:10,649 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:10 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:10,650 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:10,651 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:10,651 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:10,652 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:10,652 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:10,652 - apify_client - DEBUG - Request successful
2025-08-06 15:53:11,653 - apify_client - DEBUG - Sending request
2025-08-06 15:53:11,654 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:11,655 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:11,655 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:11,656 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:11,656 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:11,766 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:12 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:11,767 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:11,768 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:11,770 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:11,770 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:11,770 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:11,770 - apify_client - DEBUG - Request successful
2025-08-06 15:53:11,871 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:11,871 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:11,872 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:11,911 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:11,911 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:11,912 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:11,912 - apify_client - DEBUG - Request successful
2025-08-06 15:53:12,771 - apify_client - DEBUG - Sending request
2025-08-06 15:53:12,773 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:12,773 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:12,774 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:12,774 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:12,774 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:12,836 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:13 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:12,836 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:12,837 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:12,837 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:12,837 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:12,838 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:12,838 - apify_client - DEBUG - Request successful
2025-08-06 15:53:13,839 - apify_client - DEBUG - Sending request
2025-08-06 15:53:13,840 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:13,840 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:13,841 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:13,841 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:13,841 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:13,911 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:14 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:13,912 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:13,912 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:13,913 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:13,913 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:13,913 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:13,913 - apify_client - DEBUG - Request successful
2025-08-06 15:53:14,914 - apify_client - DEBUG - Sending request
2025-08-06 15:53:14,915 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:14,916 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:14,916 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:14,917 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:14,917 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:14,977 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:15 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:14,979 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:14,980 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:14,981 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:14,982 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:14,982 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:14,983 - apify_client - DEBUG - Request successful
2025-08-06 15:53:15,985 - apify_client - DEBUG - Sending request
2025-08-06 15:53:15,986 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:15,987 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:15,988 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:15,988 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:15,989 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:16,034 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:16 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:16,035 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:16,036 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:16,036 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:16,036 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:16,037 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:16,037 - apify_client - DEBUG - Request successful
2025-08-06 15:53:17,038 - apify_client - DEBUG - Sending request
2025-08-06 15:53:17,040 - httpcore.connection - DEBUG - close.started
2025-08-06 15:53:17,041 - httpcore.connection - DEBUG - close.complete
2025-08-06 15:53:17,041 - httpcore.connection - DEBUG - close.started
2025-08-06 15:53:17,042 - httpcore.connection - DEBUG - close.complete
2025-08-06 15:53:17,043 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:17,044 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:17,045 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:17,045 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:17,046 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:17,127 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:17 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:17,128 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:17,129 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:17,131 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:17,132 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:17,133 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:17,133 - apify_client - DEBUG - Request successful
2025-08-06 15:53:18,135 - apify_client - DEBUG - Sending request
2025-08-06 15:53:18,137 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:18,138 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:18,139 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:18,139 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:18,140 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:18,181 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:18 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:18,182 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/Xl8UeVTn2ngBv6Vg1 "HTTP/1.1 200 OK"
2025-08-06 15:53:18,183 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:18,183 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:18,184 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:18,184 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:18,184 - apify_client - DEBUG - Request successful
2025-08-06 15:53:18,185 - apify_client - DEBUG - Sending request
2025-08-06 15:53:18,185 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:18,186 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:18,186 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:18,186 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:18,186 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:18,250 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:18 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, HEAD, POST'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'X-Apify-Pagination-Total', b'5'), (b'X-Apify-Pagination-Offset', b'0'), (b'X-Apify-Pagination-Count', b'5'), (b'X-Apify-Pagination-Limit', b'1000'), (b'X-Apify-Pagination-Desc', b'false'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:18,251 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/datasets/MuVJgh57VJpzIDluU/items?offset=0&limit=1000 "HTTP/1.1 200 OK"
2025-08-06 15:53:18,252 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:18,263 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:18,264 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:18,264 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:18,265 - apify_client - DEBUG - Request successful
2025-08-06 15:53:18,266 - collectors.apify_facebook - INFO - Apify run completed. Retrieved 5 results
2025-08-06 15:53:28,268 - __main__ - INFO - Processing batch 2: 2 URLs
2025-08-06 15:53:28,268 - collectors.apify_facebook - INFO - Starting Apify actor run for 2 URLs
2025-08-06 15:53:28,270 - apify_client - DEBUG - Sending request
2025-08-06 15:53:28,271 - httpcore.connection - DEBUG - close.started
2025-08-06 15:53:28,272 - httpcore.connection - DEBUG - close.complete
2025-08-06 15:53:28,272 - httpcore.connection - DEBUG - connect_tcp.started host='api.apify.com' port=443 local_address=None timeout=360 socket_options=None
2025-08-06 15:53:28,334 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E589B9B0>
2025-08-06 15:53:28,335 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000241E59087A0> server_hostname='api.apify.com' timeout=360
2025-08-06 15:53:28,412 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E589B790>
2025-08-06 15:53:28,412 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-06 15:53:28,413 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:28,413 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-06 15:53:28,414 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:28,414 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-06 15:53:28,773 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Wed, 06 Aug 2025 19:53:29 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, POST'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'200'), (b'Location', b'https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:28,775 - httpx - INFO - HTTP Request: POST https://api.apify.com/v2/acts/Catqz8yCm9MEuNd8x/runs "HTTP/1.1 201 Created"
2025-08-06 15:53:28,775 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-06 15:53:28,776 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:28,777 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:28,778 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:28,778 - apify_client - DEBUG - Request successful
2025-08-06 15:53:28,779 - apify_client - DEBUG - Sending request
2025-08-06 15:53:28,780 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:28,781 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:28,782 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:28,782 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:28,783 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:28,838 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:29 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:28,839 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:28,840 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:28,840 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:28,841 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:28,841 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:28,841 - apify_client - DEBUG - Request successful
2025-08-06 15:53:28,842 - apify_client - DEBUG - Sending request
2025-08-06 15:53:28,842 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:28,842 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:28,842 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:28,843 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:28,843 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:28,901 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:29 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'ETag', b'W/"baf-gFb6yXPBELWj14Fi7YduzZggoMo"'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:28,902 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/acts/Catqz8yCm9MEuNd8x "HTTP/1.1 200 OK"
2025-08-06 15:53:28,903 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:28,903 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:28,904 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:28,904 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:28,905 - apify_client - DEBUG - Request successful
2025-08-06 15:53:28,906 - apify_client - DEBUG - Sending request
2025-08-06 15:53:28,906 - apify_client - DEBUG - Sending request
2025-08-06 15:53:28,906 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:28,907 - httpcore.connection - DEBUG - connect_tcp.started host='api.apify.com' port=443 local_address=None timeout=360 socket_options=None
2025-08-06 15:53:28,907 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:28,908 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:28,908 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:28,909 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:28,965 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E5896D50>
2025-08-06 15:53:28,966 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000241E59087A0> server_hostname='api.apify.com' timeout=360
2025-08-06 15:53:28,987 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:29 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:28,988 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:28,988 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:28,989 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:28,989 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:28,989 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:28,990 - apify_client - DEBUG - Request successful
2025-08-06 15:53:29,039 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E5896E50>
2025-08-06 15:53:29,040 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:29,041 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:29,041 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:29,041 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:29,041 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:29,148 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:29 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:29,149 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:29,150 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:29,150 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:29,150 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:29,150 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:29,151 - apify_client - DEBUG - Request successful
2025-08-06 15:53:29,151 - apify_client - DEBUG - Sending request
2025-08-06 15:53:29,151 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:29,152 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:29,153 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:29,153 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:29,153 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:29,200 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:29 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'ETag', b'W/"baf-gFb6yXPBELWj14Fi7YduzZggoMo"'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:29,200 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/acts/Catqz8yCm9MEuNd8x "HTTP/1.1 200 OK"
2025-08-06 15:53:29,200 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:29,201 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:29,201 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:29,201 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:29,201 - apify_client - DEBUG - Request successful
2025-08-06 15:53:29,203 - apify_client - DEBUG - Sending request
2025-08-06 15:53:29,203 - apify_client - DEBUG - Sending request
2025-08-06 15:53:29,203 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:29,204 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:29,205 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:29,205 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:29,205 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:29,206 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:29,206 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:29,206 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:29,206 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:29,206 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:29,368 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:29 GMT'), (b'Content-Type', b'text/plain; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-transform'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Content-Disposition', b'inline; filename="siUoi5CfK6L6OhRB0.log"')])
2025-08-06 15:53:29,369 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0/log?stream=1&raw=1 "HTTP/1.1 200 OK"
2025-08-06 15:53:29,369 - apify_client - DEBUG - Request successful
2025-08-06 15:53:29,369 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:29,991 - apify_client - DEBUG - Sending request
2025-08-06 15:53:29,993 - httpcore.connection - DEBUG - connect_tcp.started host='api.apify.com' port=443 local_address=None timeout=360 socket_options=None
2025-08-06 15:53:30,030 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E58FEB70>
2025-08-06 15:53:30,031 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000241E59087A0> server_hostname='api.apify.com' timeout=360
2025-08-06 15:53:30,106 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000241E58FEC60>
2025-08-06 15:53:30,107 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:30,108 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:30,108 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:30,109 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:30,109 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:30,164 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:30 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:30,165 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:30,166 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:30,167 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:30,168 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:30,168 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:30,169 - apify_client - DEBUG - Request successful
2025-08-06 15:53:30,297 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:30 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:30,298 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0?waitForFinish=999999 "HTTP/1.1 200 OK"
2025-08-06 15:53:30,299 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:31,170 - apify_client - DEBUG - Sending request
2025-08-06 15:53:31,172 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:31,173 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:31,173 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:31,174 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:31,175 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:31,272 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:31 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:31,273 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:31,273 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:31,273 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:31,274 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:31,274 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:31,274 - apify_client - DEBUG - Request successful
2025-08-06 15:53:32,275 - apify_client - DEBUG - Sending request
2025-08-06 15:53:32,277 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:32,279 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:32,280 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:32,280 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:32,281 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:32,332 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:32 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:32,334 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:32,342 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:32,343 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:32,343 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:32,344 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:32,344 - apify_client - DEBUG - Request successful
2025-08-06 15:53:33,345 - apify_client - DEBUG - Sending request
2025-08-06 15:53:33,347 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:33,348 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:33,349 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:33,349 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:33,350 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:33,414 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:33 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:33,415 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:33,416 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:33,416 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:33,417 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:33,417 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:33,417 - apify_client - DEBUG - Request successful
2025-08-06 15:53:34,419 - apify_client - DEBUG - Sending request
2025-08-06 15:53:34,420 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:34,421 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:34,421 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:34,421 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:34,422 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:34,517 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:34 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:34,518 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:34,519 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:34,520 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:34,521 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:34,521 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:34,522 - apify_client - DEBUG - Request successful
2025-08-06 15:53:35,523 - apify_client - DEBUG - Sending request
2025-08-06 15:53:35,525 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:35,526 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:35,527 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:35,527 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:35,528 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:35,615 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:35 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:35,616 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:35,617 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:35,618 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:35,618 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:35,618 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:35,618 - apify_client - DEBUG - Request successful
2025-08-06 15:53:35,965 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:35,966 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:35,967 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:36,015 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:36,016 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:36,017 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:36,018 - apify_client - DEBUG - Request successful
2025-08-06 15:53:36,619 - apify_client - DEBUG - Sending request
2025-08-06 15:53:36,621 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:36,621 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:36,622 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:36,623 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:36,623 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:36,682 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:37 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:36,684 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:36,685 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:36,686 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:36,686 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:36,686 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:36,687 - apify_client - DEBUG - Request successful
2025-08-06 15:53:37,689 - apify_client - DEBUG - Sending request
2025-08-06 15:53:37,689 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:37,690 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:37,690 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:37,690 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:37,691 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:37,818 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:38 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:37,819 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:37,820 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:37,821 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:37,822 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:37,822 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:37,823 - apify_client - DEBUG - Request successful
2025-08-06 15:53:38,824 - apify_client - DEBUG - Sending request
2025-08-06 15:53:38,826 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:38,827 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:38,828 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:38,828 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:38,829 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:39,047 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:39 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:39,049 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:39,050 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:39,051 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:39,052 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:39,052 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:39,053 - apify_client - DEBUG - Request successful
2025-08-06 15:53:40,054 - apify_client - DEBUG - Sending request
2025-08-06 15:53:40,056 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:40,057 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:40,058 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:40,059 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:40,059 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:40,268 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:40 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:40,269 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:40,270 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:40,271 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:40,271 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:40,272 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:40,273 - apify_client - DEBUG - Request successful
2025-08-06 15:53:41,274 - apify_client - DEBUG - Sending request
2025-08-06 15:53:41,275 - httpcore.connection - DEBUG - close.started
2025-08-06 15:53:41,276 - httpcore.connection - DEBUG - close.complete
2025-08-06 15:53:41,277 - httpcore.connection - DEBUG - close.started
2025-08-06 15:53:41,278 - httpcore.connection - DEBUG - close.complete
2025-08-06 15:53:41,278 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:41,280 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:41,280 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:41,281 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:41,281 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:41,352 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:41 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:41,352 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:41,353 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:41,353 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:41,354 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:41,354 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:41,354 - apify_client - DEBUG - Request successful
2025-08-06 15:53:42,356 - apify_client - DEBUG - Sending request
2025-08-06 15:53:42,357 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:42,358 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:42,359 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:42,359 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:42,360 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:42,423 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:42 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, PUT, DELETE'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:42,424 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/actor-runs/siUoi5CfK6L6OhRB0 "HTTP/1.1 200 OK"
2025-08-06 15:53:42,424 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:42,425 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:42,425 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:42,425 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:42,425 - apify_client - DEBUG - Request successful
2025-08-06 15:53:42,426 - apify_client - DEBUG - Sending request
2025-08-06 15:53:42,426 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-06 15:53:42,426 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-06 15:53:42,427 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-06 15:53:42,427 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-06 15:53:42,427 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-06 15:53:42,506 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 06 Aug 2025 19:53:42 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Cache-Control', b'no-cache, no-store, must-revalidate'), (b'Pragma', b'no-cache'), (b'Expires', b'0'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Headers', b'User-Agent, Content-Type, Authorization, X-Apify-Request-Origin, openai-conversation-id, openai-ephemeral-user-id'), (b'Access-Control-Allow-Methods', b'GET, HEAD, POST'), (b'Access-Control-Expose-Headers', b'X-Apify-Pagination-Total, X-Apify-Pagination-Offset, X-Apify-Pagination-Desc, X-Apify-Pagination-Count, X-Apify-Pagination-Limit'), (b'Referrer-Policy', b'no-referrer'), (b'X-Robots-Tag', b'none'), (b'X-RateLimit-Limit', b'30'), (b'X-Apify-Pagination-Total', b'2'), (b'X-Apify-Pagination-Offset', b'0'), (b'X-Apify-Pagination-Count', b'2'), (b'X-Apify-Pagination-Limit', b'1000'), (b'X-Apify-Pagination-Desc', b'false'), (b'Vary', b'Accept-Encoding'), (b'Content-Encoding', b'gzip')])
2025-08-06 15:53:42,507 - httpx - INFO - HTTP Request: GET https://api.apify.com/v2/datasets/tIvkYjdmUhfb7miwG/items?offset=0&limit=1000 "HTTP/1.1 200 OK"
2025-08-06 15:53:42,507 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-06 15:53:42,511 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-06 15:53:42,512 - httpcore.http11 - DEBUG - response_closed.started
2025-08-06 15:53:42,512 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-06 15:53:42,512 - apify_client - DEBUG - Request successful
2025-08-06 15:53:42,513 - collectors.apify_facebook - INFO - Apify run completed. Retrieved 2 results
2025-08-06 15:53:42,527 - __main__ - INFO - Found email for https://www.facebook.com/LaurelHighlandsInsuranceGroup: <EMAIL>
2025-08-06 15:53:42,527 - __main__ - INFO - Found email for https://www.facebook.com/secondopinionins: <EMAIL>
2025-08-06 15:53:42,528 - __main__ - INFO - Found email for https://www.facebook.com/secondopinionins: <EMAIL>
2025-08-06 15:53:42,528 - __main__ - INFO - Found email for https://www.facebook.com/Schultzinsurancegroup1: <EMAIL>
2025-08-06 15:53:42,531 - __main__ - INFO - Results saved to carminstest_contacts_20250806_150823_with_facebook_emails.csv
2025-08-06 15:53:42,533 - __main__ - INFO - Extraction completed successfully!
2025-08-06 15:53:42,533 - __main__ - INFO - Total businesses: 24
2025-08-06 15:53:42,534 - __main__ - INFO - Facebook emails extracted: 4
2025-08-06 15:53:42,534 - __main__ - INFO - Results saved to: carminstest_contacts_20250806_150823_with_facebook_emails.csv
2025-08-06 15:53:42,536 - __main__ - INFO - Success rate: 57.1%
2025-08-06 15:53:42,538 - __main__ - INFO - Examples of extracted emails:
2025-08-06 15:53:42,538 - __main__ - INFO -   Laurel Highlands Insurance Group LLC: <EMAIL>
2025-08-06 15:53:42,539 - __main__ - INFO -   Second Opinion Insurance Agency: <EMAIL>
2025-08-06 15:53:42,539 - __main__ - INFO -   Nationwide Insurance Christopher James Carilli Agency: <EMAIL>
