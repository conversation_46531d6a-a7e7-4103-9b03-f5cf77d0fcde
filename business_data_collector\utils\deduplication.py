"""
Deduplication utilities for business data.
"""
import logging
from typing import List, Dict, Any, Set, Tuple
import re
from difflib import SequenceMatcher

logger = logging.getLogger(__name__)


class BusinessDeduplicator:
    """Handles deduplication of business data from multiple sources."""
    
    def __init__(self, similarity_threshold: float = 0.8):
        """Initialize the deduplicator.
        
        Args:
            similarity_threshold: Minimum similarity score for considering businesses as duplicates
        """
        self.similarity_threshold = similarity_threshold
        
    def deduplicate_businesses(self, businesses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate businesses from the list.
        
        Args:
            businesses: List of business data dictionaries
            
        Returns:
            List of deduplicated business data dictionaries
        """
        if not businesses:
            return []
            
        logger.info(f"Starting deduplication of {len(businesses)} businesses...")
        
        # First pass: exact matches
        unique_businesses = self._remove_exact_duplicates(businesses)
        logger.info(f"After exact duplicate removal: {len(unique_businesses)} businesses")
        
        # Second pass: fuzzy matching
        unique_businesses = self._remove_fuzzy_duplicates(unique_businesses)
        logger.info(f"After fuzzy duplicate removal: {len(unique_businesses)} businesses")
        
        return unique_businesses
    
    def _remove_exact_duplicates(self, businesses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove exact duplicates based on name, address, and phone.
        
        Args:
            businesses: List of business data dictionaries
            
        Returns:
            List of businesses with exact duplicates removed
        """
        seen_businesses: Set[Tuple[str, str, str]] = set()
        unique_businesses = []
        
        for business in businesses:
            # Create a key from normalized name, address, and phone
            key = self._create_business_key(business)
            
            if key not in seen_businesses:
                seen_businesses.add(key)
                unique_businesses.append(business)
            else:
                # Merge data from duplicate (prefer more complete data)
                existing_business = self._find_business_by_key(unique_businesses, key)
                if existing_business:
                    self._merge_business_data(existing_business, business)
                    
        return unique_businesses
    
    def _remove_fuzzy_duplicates(self, businesses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove fuzzy duplicates using similarity matching.
        
        Args:
            businesses: List of business data dictionaries
            
        Returns:
            List of businesses with fuzzy duplicates removed
        """
        unique_businesses = []
        
        for business in businesses:
            is_duplicate = False
            
            for existing_business in unique_businesses:
                if self._are_businesses_similar(business, existing_business):
                    # Merge data from duplicate
                    self._merge_business_data(existing_business, business)
                    is_duplicate = True
                    break
                    
            if not is_duplicate:
                unique_businesses.append(business)
                
        return unique_businesses
    
    def _create_business_key(self, business: Dict[str, Any]) -> Tuple[str, str, str]:
        """Create a normalized key for exact duplicate detection.
        
        Args:
            business: Business data dictionary
            
        Returns:
            Tuple of normalized (name, address, phone)
        """
        name = self._normalize_text(business.get('name', ''))
        address = self._normalize_text(business.get('address', ''))
        phone = self._normalize_phone(business.get('phone', ''))
        
        return (name, address, phone)
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for comparison.
        
        Args:
            text: Text to normalize
            
        Returns:
            Normalized text
        """
        if not text:
            return ''
            
        # Convert to lowercase
        text = text.lower()

        # Remove common prefixes and suffixes
        prefixes = [r'^(the|a|an)\s+']
        suffixes = [
            r'\s+(inc|llc|corp|corporation|company|co|ltd|limited)\.?$',
            r'\s+(restaurant|cafe|bar|shop|store|services?)\.?$'
        ]

        for prefix in prefixes:
            text = re.sub(prefix, '', text)

        for suffix in suffixes:
            text = re.sub(suffix, '', text)
        
        # Remove special characters and extra spaces (keep apostrophes for now)
        text = re.sub(r'[^\w\s\']', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _normalize_phone(self, phone: str) -> str:
        """Normalize phone number for comparison.
        
        Args:
            phone: Phone number to normalize
            
        Returns:
            Normalized phone number (digits only)
        """
        if not phone:
            return ''
            
        # Extract digits only
        digits = re.sub(r'\D', '', phone)
        
        # Remove country code if present
        if len(digits) == 11 and digits.startswith('1'):
            digits = digits[1:]
            
        return digits
    
    def _find_business_by_key(self, businesses: List[Dict[str, Any]], key: Tuple[str, str, str]) -> Dict[str, Any]:
        """Find business in list by key.
        
        Args:
            businesses: List of business data dictionaries
            key: Business key tuple
            
        Returns:
            Business data dictionary or None if not found
        """
        for business in businesses:
            if self._create_business_key(business) == key:
                return business
        return None
    
    def _are_businesses_similar(self, business1: Dict[str, Any], business2: Dict[str, Any]) -> bool:
        """Check if two businesses are similar enough to be considered duplicates.
        
        Args:
            business1: First business data dictionary
            business2: Second business data dictionary
            
        Returns:
            True if businesses are similar, False otherwise
        """
        # Compare normalized names
        name1 = self._normalize_text(business1.get('name', ''))
        name2 = self._normalize_text(business2.get('name', ''))
        
        if not name1 or not name2:
            return False
            
        name_similarity = SequenceMatcher(None, name1, name2).ratio()
        
        # If names are very similar, check address
        if name_similarity >= self.similarity_threshold:
            address1 = self._normalize_text(business1.get('address', ''))
            address2 = self._normalize_text(business2.get('address', ''))
            
            if address1 and address2:
                address_similarity = SequenceMatcher(None, address1, address2).ratio()
                return address_similarity >= 0.6  # Lower threshold for address
            else:
                # If no address, check phone
                phone1 = self._normalize_phone(business1.get('phone', ''))
                phone2 = self._normalize_phone(business2.get('phone', ''))
                
                if phone1 and phone2:
                    return phone1 == phone2
                    
                # If no phone either, rely on name similarity
                return name_similarity >= 0.9
                
        return False
    
    def _merge_business_data(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """Merge data from source business into target business.
        
        Args:
            target: Target business data dictionary (modified in place)
            source: Source business data dictionary
        """
        # Merge fields, preferring non-empty values
        for key, value in source.items():
            if key == 'source':
                # Combine sources
                target_sources = target.get('source', '').split(',')
                source_sources = value.split(',')
                all_sources = list(set(target_sources + source_sources))
                target['source'] = ','.join(filter(None, all_sources))
            elif not target.get(key) and value:
                # Use source value if target is empty
                target[key] = value
            elif key in ['types'] and isinstance(value, list):
                # Merge type lists
                target_types = target.get('types', [])
                if isinstance(target_types, list):
                    target['types'] = list(set(target_types + value))
                    
        # Update collection timestamp to latest
        if 'collected_at' in source:
            target['collected_at'] = max(
                target.get('collected_at', ''),
                source['collected_at']
            )
