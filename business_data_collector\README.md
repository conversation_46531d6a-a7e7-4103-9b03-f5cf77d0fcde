# Business Data Collector

A comprehensive data collection script that gathers business information from multiple sources for web scraping purposes. This tool collects business URLs and contact information from Google Places API, Overpass API (OpenStreetMap), and Yellow Pages within a 10-mile radius of Pittsburgh, PA.

## Features

- **Multi-source data collection**: Combines data from Google Places API, Overpass API, and Yellow Pages
- **Geographic filtering**: Searches within a 10-mile radius of Pittsburgh
- **Corporate filtering**: Excludes major corporate chains and franchises to focus on local businesses
- **Data deduplication**: Removes duplicate entries based on business name, address, and phone number
- **Rate limiting**: Respectful API usage with configurable delays
- **Comprehensive logging**: Detailed logging for monitoring and debugging
- **Flexible output**: Exports data to CSV format with customizable fields

## Project Structure

```
business_data_collector/
├── README.md
├── requirements.txt
├── config.py
├── main.py
├── collectors/
│   ├── __init__.py
│   ├── google_places.py
│   ├── overpass_api.py
│   └── yellow_pages.py
├── utils/
│   ├── __init__.py
│   ├── deduplication.py
│   ├── filtering.py
│   └── data_export.py
├── tests/
│   ├── __init__.py
│   ├── test_google_places.py
│   ├── test_overpass_api.py
│   └── test_yellow_pages.py
└── output/
    └── (generated CSV files)
```

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables (optional):
Create a `.env` file in the project root with your API keys:
```
GOOGLE_PLACES_API_KEY=your_api_key_here
```

## Usage

Run the main data collection script:
```bash
python main.py
```

The script will:
1. Collect business data from all three sources
2. Apply filtering to exclude corporate chains
3. Remove duplicate entries
4. Export results to `output/pittsburgh_businesses.csv`

## Configuration

Edit `config.py` to customize:
- Search location and radius
- API keys and rate limits
- Business types to include/exclude
- Corporate blacklist
- Output format and location

## Data Sources

### Google Places API
- Most comprehensive and reliable source
- Requires API key (provided in config)
- Returns structured business data including websites
- Rate limited to 1 request per second

### Overpass API
- Free OpenStreetMap data
- Good coverage of local businesses
- No API key required
- Queries for amenities and shops

### Yellow Pages
- Web scraping approach
- Additional business coverage
- Respectful scraping with delays
- Extracts business listings and contact info

## Output Format

The generated CSV file includes:
- Business name
- Full address
- Phone number
- Website URL
- Business category/type
- Data source
- Collection timestamp

## Testing

Run the test suite:
```bash
python -m pytest tests/
```

## Legal and Ethical Considerations

- Respects rate limits and terms of service for all data sources
- Implements appropriate delays between requests
- Focuses on publicly available business information
- Excludes personal or private data

## License

This project is for educational and research purposes. Please ensure compliance with all applicable terms of service and local regulations when using this tool.
