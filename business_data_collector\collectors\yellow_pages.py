"""
Yellow Pages scraper for business data using crawl4ai with LLM extraction.
"""
import asyncio
import logging
import json
import re
import time
import os
import sys
import contextlib
from typing import List, Dict, Any, Optional, Set
from urllib.parse import urljoin, quote_plus
from pydantic import BaseModel, Field
from crawl4ai import AsyncWebCrawler, BrowserConfig, CacheMode, CrawlerRunConfig, LLMExtractionStrategy, LLMConfig
from config import YELLOW_PAGES_DELAY

# Suppress LiteLLM verbose output
os.environ["LITELLM_LOG"] = "ERROR"
import litellm
litellm.suppress_debug_info = True

# Also suppress specific loggers
logging.getLogger("litellm").setLevel(logging.ERROR)
logging.getLogger("httpx").setLevel(logging.ERROR)

logger = logging.getLogger(__name__)


@contextlib.contextmanager
def suppress_stderr():
    """Context manager to suppress stderr output."""
    with open(os.devnull, "w") as devnull:
        old_stderr = sys.stderr
        sys.stderr = devnull
        try:
            yield
        finally:
            sys.stderr = old_stderr


class BusinessData(BaseModel):
    """Pydantic model for business data extraction."""
    name: str = Field(..., description="The name of the business")
    address: str = Field(..., description="The full address of the business")
    phone_number: str = Field(..., description="The phone number of the business")
    website: str = Field(default="", description="The main website URL of the business if available")
    facebook: str = Field(default="", description="Facebook page URL if available")
    instagram: str = Field(default="", description="Instagram profile URL if available")
    twitter: str = Field(default="", description="Twitter profile URL if available")
    linkedin: str = Field(default="", description="LinkedIn profile URL if available")
    yelp: str = Field(default="", description="Yelp page URL if available")
    description: str = Field(default="", description="A brief description of the business if available")


class YellowPagesCollector:
    """Collector for business data from Yellow Pages website using crawl4ai."""

    def __init__(self, llm_provider: str = "groq/llama-3.1-8b-instant", api_key: Optional[str] = None):
        """Initialize the Yellow Pages collector.

        Args:
            llm_provider: LLM provider for extraction (e.g., "groq/llama-3.1-8b-instant")
            api_key: API key for the LLM provider
        """
        self.base_url = "https://www.yellowpages.com"
        self.llm_provider = llm_provider
        self.api_key = api_key
        self.css_selector = ".result, .search-results .result, .organic .result, [data-listing-id], .listing, .business-card, .info"
        self.seen_names: Set[str] = set()

    def _get_browser_config(self) -> BrowserConfig:
        """Get browser configuration for crawl4ai optimized for speed."""
        return BrowserConfig(
            browser_type="chromium",
            headless=True,
            verbose=False,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",
                "--disable-gpu",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding"
            ]
        )

    def _get_llm_strategy(self) -> LLMExtractionStrategy:
        """Get LLM extraction strategy for business data."""
        instructions = """
        Extract business information from the Yellow Pages listings on this page.
        Look for business listings, business cards, or any business information displayed.

        For each business listing found, extract:
        - name: The business name (required)
        - address: The full address including street, city, state (if available)
        - phone_number: The phone number in any format (if available)
        - website: The main website URL if available (empty string if not found)
        - facebook: Facebook page URL if available (empty string if not found)
        - instagram: Instagram profile URL if available (empty string if not found)
        - twitter: Twitter profile URL if available (empty string if not found)
        - linkedin: LinkedIn profile URL if available (empty string if not found)
        - yelp: Yelp page URL if available (empty string if not found)
        - description: Any business description, category, or type information if available

        IMPORTANT:
        - Only extract businesses that have at least a name and either an address or phone number
        - Look carefully for any website URLs, social media links, or online presence information
        - Extract ALL URLs you find - website, Facebook, Instagram, Twitter, LinkedIn, Yelp, etc.
        - If you find business information, extract it even if some fields are missing
        - Return the data as a JSON array of business objects
        - If no businesses are found, return an empty array []
        """

        llm_config = LLMConfig(
            provider=self.llm_provider,
            api_token=self.api_key
        )

        return LLMExtractionStrategy(
            llm_config=llm_config,
            schema=BusinessData.model_json_schema(),
            extraction_type="schema",
            instruction=instructions,
            input_format="markdown",
            verbose=False,
        )

    async def _check_no_results(self, crawler, url: str) -> bool:
        """Check if the page shows 'No Results Found'."""
        try:
            result = await crawler.arun(
                url=url,
                config=CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    session_id="yellowpages_check",
                ),
            )

            if result.success and result.cleaned_html:
                return "No Results Found" in result.cleaned_html

        except Exception as e:
            logger.debug(f"Error checking for no results: {str(e)}")

        return False

    def _is_duplicate(self, name: str) -> bool:
        """Check if business name is a duplicate."""
        normalized_name = name.lower().strip()
        return normalized_name in {n.lower().strip() for n in self.seen_names}
        
    def collect_businesses(self,
                          location: str = "Pittsburgh, PA",
                          categories: Optional[List[str]] = None,
                          max_results: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect business data from Yellow Pages.

        Args:
            location: Location to search in
            categories: List of business categories to search for
            max_results: Maximum number of results to collect (for testing)

        Returns:
            List of business data dictionaries
        """
        if categories is None:
            categories = [
                "restaurants",
                "retail stores",
                "auto repair",
                "beauty salons",
                "medical services",
                "professional services",
                "home services",
                "financial services"
            ]

        # Run async collection
        try:
            # Check if we're already in an event loop
            asyncio.get_running_loop()
            # If we are, we can't use asyncio.run(), so we need to handle this differently
            # For now, let's create a new event loop in a thread
            import concurrent.futures

            def run_in_thread():
                return asyncio.run(self._collect_businesses_async(location, categories, max_results))

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                return future.result()
        except RuntimeError:
            # No event loop running, safe to use asyncio.run()
            return asyncio.run(self._collect_businesses_async(location, categories, max_results))

    async def _collect_businesses_async(self, location: str, categories: List[str], max_results: Optional[int] = None) -> List[Dict[str, Any]]:
        """Async method to collect business data from Yellow Pages."""
        businesses = []

        # Skip if no API key provided
        if not self.api_key:
            logger.warning("No API key provided for LLM extraction. Skipping Yellow Pages.")
            return []

        browser_config = self._get_browser_config()

        async with AsyncWebCrawler(config=browser_config, verbose=False) as crawler:
            for i, category in enumerate(categories, 1):
                start_time = time.time()
                print(f"[{i}/{len(categories)}] Searching Yellow Pages for {category} in {location}...")
                logger.info(f"Searching Yellow Pages for {category} in {location}...")

                try:
                    category_businesses = await self._search_category_async(crawler, category, location)
                    businesses.extend(category_businesses)

                    elapsed = time.time() - start_time
                    print(f"Found {len(category_businesses)} businesses for {category} in {elapsed:.1f}s")

                    # Check if we've reached the max_results limit
                    if max_results and len(businesses) >= max_results:
                        print(f"Reached max_results limit of {max_results}. Stopping collection.")
                        businesses = businesses[:max_results]
                        break

                    # Minimal delay for speed
                    await asyncio.sleep(0.1)

                except Exception as e:
                    logger.error(f"Error searching for {category}: {str(e)}")
                    continue

        print(f"Yellow Pages collection complete: {len(businesses)} businesses found")
        logger.info(f"Collected {len(businesses)} businesses from Yellow Pages")
        return businesses
    
    async def _search_category_async(self, crawler, category: str, location: str, max_pages: int = 1) -> List[Dict[str, Any]]:
        """Search for businesses in a specific category using crawl4ai.

        Args:
            crawler: AsyncWebCrawler instance
            category: Business category to search for
            location: Location to search in
            max_pages: Maximum number of pages to scrape

        Returns:
            List of business data dictionaries
        """
        businesses = []

        for page in range(1, max_pages + 1):
            try:
                page_businesses = await self._scrape_search_page_async(crawler, category, location, page)
                businesses.extend(page_businesses)

                # If we got fewer results than expected, we've reached the end
                if len(page_businesses) < 3:  # Even more aggressive threshold for speed
                    break

                # Minimal delay between pages for speed
                await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"Error scraping page {page} for {category}: {str(e)}")
                break

        return businesses
    
    async def _scrape_search_page_async(self, crawler, category: str, location: str, page: int) -> List[Dict[str, Any]]:
        """Scrape a single search results page using crawl4ai with LLM extraction.

        Args:
            crawler: AsyncWebCrawler instance
            category: Business category to search for
            location: Location to search in
            page: Page number to scrape

        Returns:
            List of business data dictionaries
        """
        # Construct search URL
        search_url = f"{self.base_url}/search"
        params = {
            'search_terms': category,
            'geo_location_terms': location,
            'page': page
        }

        # Build full URL with parameters
        param_string = '&'.join([f"{k}={quote_plus(str(v))}" for k, v in params.items()])
        full_url = f"{search_url}?{param_string}"

        logger.debug(f"Scraping URL: {full_url}")

        try:
            # Check for "No Results Found" first
            no_results = await self._check_no_results(crawler, full_url)
            if no_results:
                logger.info(f"No more results found for {category} page {page}")
                return []

            # Get LLM strategy
            llm_strategy = self._get_llm_strategy()

            # Crawl with LLM extraction (suppress LiteLLM debug messages)
            with suppress_stderr():
                result = await crawler.arun(
                    url=full_url,
                    config=CrawlerRunConfig(
                        cache_mode=CacheMode.BYPASS,
                        extraction_strategy=llm_strategy,
                        session_id=f"yellowpages_{category}_{location}",
                    ),
                )

            if not result.success:
                logger.warning(f"Failed to crawl {full_url}: {result.error_message}")
                return []

            businesses = []

            # Parse extracted data
            if result.extracted_content:
                try:
                    extracted_data = json.loads(result.extracted_content)
                    logger.debug(f"Extracted data: {extracted_data}")

                    # Handle both list and single object responses
                    if isinstance(extracted_data, list):
                        business_list = extracted_data
                    else:
                        business_list = [extracted_data]

                    for business_data in business_list:
                        if isinstance(business_data, dict):
                            business = self._process_extracted_business(business_data)
                            if business and not self._is_duplicate(business['name']):
                                self.seen_names.add(business['name'])
                                businesses.append(business)

                except (json.JSONDecodeError, KeyError) as e:
                    logger.error(f"Error parsing extracted data: {str(e)}")

            logger.info(f"Extracted {len(businesses)} businesses from {category} page {page}")
            return businesses

        except Exception as e:
            logger.error(f"Error crawling {category} page {page}: {str(e)}")
            return []
    
    def _process_extracted_business(self, business_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process extracted business data from LLM extraction.

        Args:
            business_data: Raw extracted business data from LLM

        Returns:
            Processed business data dictionary or None if invalid
        """
        try:
            # Skip if error flag is present and True
            if business_data.get('error') is True:
                return None

            # Extract and clean business name
            name = business_data.get('name', '').strip()
            if not name:
                return None

            # Extract and clean address
            address = business_data.get('address', '').strip()

            # Extract and clean phone number
            phone = business_data.get('phone_number', business_data.get('phone', '')).strip()
            if phone:
                # Clean phone number
                phone = re.sub(r'[^\d\-\(\)\+\s]', '', phone)

            # Extract and clean website
            website = business_data.get('website', '').strip()
            if website and not website.startswith('http') and website:
                if website.startswith('www.'):
                    website = f"https://{website}"
                elif '.' in website:
                    website = f"https://{website}"
                else:
                    website = ""

            # Extract and clean social media URLs
            facebook = business_data.get('facebook', '').strip()
            instagram = business_data.get('instagram', '').strip()
            twitter = business_data.get('twitter', '').strip()
            linkedin = business_data.get('linkedin', '').strip()
            yelp = business_data.get('yelp', '').strip()

            # Clean social media URLs
            def clean_social_url(url, platform):
                if not url:
                    return ""
                if not url.startswith('http'):
                    if platform == 'facebook' and 'facebook.com' not in url:
                        return f"https://facebook.com/{url.lstrip('/')}"
                    elif platform == 'instagram' and 'instagram.com' not in url:
                        return f"https://instagram.com/{url.lstrip('/')}"
                    elif platform == 'twitter' and 'twitter.com' not in url and 'x.com' not in url:
                        return f"https://twitter.com/{url.lstrip('/')}"
                    elif platform == 'linkedin' and 'linkedin.com' not in url:
                        return f"https://linkedin.com/company/{url.lstrip('/')}"
                    elif '.' in url:
                        return f"https://{url}"
                return url

            facebook = clean_social_url(facebook, 'facebook')
            instagram = clean_social_url(instagram, 'instagram')
            twitter = clean_social_url(twitter, 'twitter')
            linkedin = clean_social_url(linkedin, 'linkedin')
            yelp = clean_social_url(yelp, 'yelp')

            # Extract description and use as category
            description = business_data.get('description', '').strip()
            categories = [description] if description else []

            # Validate minimum requirements
            if not name or (not address and not phone):
                return None

            processed_business = {
                'name': name,
                'address': address,
                'phone': phone,
                'website': website,
                'facebook': facebook,
                'instagram': instagram,
                'twitter': twitter,
                'linkedin': linkedin,
                'yelp': yelp,
                'types': categories,
                'primary_type': categories[0] if categories else '',
                'business_status': 'OPERATIONAL',
                'source': 'yellow_pages_llm'
            }

            return processed_business

        except Exception as e:
            logger.error(f"Error processing extracted business: {str(e)}")
            return None
