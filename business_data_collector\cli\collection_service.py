"""
Unified collection service that integrates CLI with existing collectors
"""
import logging
import sys
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.panel import Panel

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from collectors.google_places import GooglePlacesCollector
from collectors.overpass_api import OverpassAPICollector
from collectors.yellow_pages import YellowPagesCollector
from utils.deduplication import BusinessDeduplicator
from utils.filtering import BusinessFilter
from utils.data_export import DataExporter
from cli.categories import CategoryManager
from cli.location import LocationService
from config import GROQ_API_KEY

console = Console()
logger = logging.getLogger(__name__)


class CollectionService:
    """Service for coordinating business data collection from multiple sources"""
    
    def __init__(self):
        self.category_manager = CategoryManager()
        self.location_service = LocationService()
        self.deduplicator = BusinessDeduplicator()
        self.filter = BusinessFilter()
        self.exporter = DataExporter()
    
    def collect_businesses(self,
                          category_ids: List[str],
                          location_data: Dict[str, Any],
                          radius_miles: float,
                          sources: List[str],
                          output_config: Optional[Dict[str, Any]] = None,
                          max_results_per_category: Optional[int] = None) -> Dict[str, Any]:
        """
        Collect businesses from multiple sources with CLI integration

        Args:
            category_ids: List of unified category IDs
            location_data: Location information with lat/lng
            radius_miles: Search radius in miles
            sources: List of data sources to use
            output_config: Output configuration
            max_results_per_category: Limit results per category (for testing)

        Returns:
            Dictionary with collection results and metadata
        """
        start_time = datetime.now()
        all_businesses = []
        collection_stats = {
            'total_collected': 0,
            'by_source': {},
            'source_status': {},  # Track success/failure of each source
            'categories_searched': category_ids,
            'location': location_data,
            'radius_miles': radius_miles,
            'start_time': start_time,
            'sources_used': sources
        }
        
        # Convert radius to meters
        radius_meters = self.location_service.miles_to_meters(radius_miles)
        
        console.print(Panel(
            f"[bold]Starting Business Collection[/bold]\n\n"
            f"Location: {location_data['name']}\n"
            f"Coordinates: {location_data['latitude']}, {location_data['longitude']}\n"
            f"Radius: {radius_miles} miles ({radius_meters} meters)\n"
            f"Categories: {len(category_ids)} selected\n"
            f"Sources: {', '.join(sources)}",
            title="Collection Configuration",
            border_style="blue"
        ))
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console
        ) as progress:
            
            main_task = progress.add_task("Collecting businesses...", total=len(sources))
            
            # Collect from each source
            if "google" in sources:
                progress.update(main_task, description="Collecting from Google Places...")
                try:
                    google_businesses = self._collect_from_google_places(
                        category_ids, location_data, radius_meters, progress, max_results_per_category
                    )
                    all_businesses.extend(google_businesses)
                    collection_stats['by_source']['google'] = len(google_businesses)
                    collection_stats['source_status']['google'] = 'success' if google_businesses else 'no_results'
                    if google_businesses:
                        console.print(f"[green]✅ Google Places: {len(google_businesses)} businesses collected[/green]")
                    else:
                        console.print(f"[yellow]⚠️  Google Places: No businesses found[/yellow]")
                except Exception as e:
                    collection_stats['by_source']['google'] = 0
                    collection_stats['source_status']['google'] = f'failed: {str(e)}'
                    console.print(f"[red]❌ Google Places: Failed - {str(e)}[/red]")
                progress.advance(main_task)

            if "overpass" in sources:
                progress.update(main_task, description="Collecting from Overpass API...")
                try:
                    overpass_businesses = self._collect_from_overpass(
                        category_ids, location_data, radius_meters, progress, max_results_per_category
                    )
                    all_businesses.extend(overpass_businesses)
                    collection_stats['by_source']['overpass'] = len(overpass_businesses)
                    collection_stats['source_status']['overpass'] = 'success' if overpass_businesses else 'no_results'
                    if overpass_businesses:
                        console.print(f"[green]✅ Overpass API: {len(overpass_businesses)} businesses collected[/green]")
                    else:
                        console.print(f"[yellow]⚠️  Overpass API: No businesses found[/yellow]")
                except Exception as e:
                    collection_stats['by_source']['overpass'] = 0
                    collection_stats['source_status']['overpass'] = f'failed: {str(e)}'
                    console.print(f"[red]❌ Overpass API: Failed - {str(e)}[/red]")
                progress.advance(main_task)

            if "yellowpages" in sources:
                progress.update(main_task, description="Collecting from Yellow Pages...")
                try:
                    yellowpages_businesses = self._collect_from_yellow_pages(
                        category_ids, location_data, progress, max_results_per_category
                    )
                    all_businesses.extend(yellowpages_businesses)
                    collection_stats['by_source']['yellowpages'] = len(yellowpages_businesses)
                    collection_stats['source_status']['yellowpages'] = 'success' if yellowpages_businesses else 'no_results'
                    if yellowpages_businesses:
                        console.print(f"[green]✅ Yellow Pages: {len(yellowpages_businesses)} businesses collected[/green]")
                    else:
                        console.print(f"[yellow]⚠️  Yellow Pages: No businesses found[/yellow]")
                except Exception as e:
                    collection_stats['by_source']['yellowpages'] = 0
                    collection_stats['source_status']['yellowpages'] = f'failed: {str(e)}'
                    console.print(f"[red]❌ Yellow Pages: Failed - {str(e)}[/red]")
                progress.advance(main_task)
            
            # Process collected data
            progress.update(main_task, description="Processing collected data...")
            processed_businesses = self._process_businesses(all_businesses, progress)
            
            # Export results
            if output_config:
                progress.update(main_task, description="Exporting results...")
                export_paths = self._export_results(processed_businesses, output_config, progress)
                collection_stats['export_paths'] = export_paths
        
        # Final statistics
        end_time = datetime.now()
        collection_stats.update({
            'total_collected': len(all_businesses),
            'total_after_processing': len(processed_businesses),
            'end_time': end_time,
            'duration': end_time - start_time
        })
        
        self._show_collection_summary(collection_stats)
        
        return {
            'businesses': processed_businesses,
            'stats': collection_stats
        }
    
    def _collect_from_google_places(self, category_ids: List[str], location_data: Dict[str, Any],
                                   radius_meters: int, progress: Progress, max_results: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect from Google Places API"""
        try:
            # Get Google Places types for categories
            google_types = self.category_manager.get_google_places_types(category_ids)

            collector = GooglePlacesCollector()

            if not google_types:
                console.print("[yellow]No Google Places types found for selected categories[/yellow]")
                return []

            console.print(f"[dim]Searching Google Places for types: {', '.join(google_types)}[/dim]")

            # Use the selected business types instead of all types
            businesses = collector.collect_businesses(
                lat=location_data['latitude'],
                lng=location_data['longitude'],
                radius=radius_meters,
                max_results=max_results or 15,  # Reduced default to save API calls
                business_types=google_types
            )

            console.print(f"[green]Google Places: Collected {len(businesses)} businesses[/green]")
            return businesses

        except Exception as e:
            error_msg = str(e)
            if "403" in error_msg or "forbidden" in error_msg.lower():
                console.print(f"[red]❌ Google Places API Error: {error_msg}[/red]")
                console.print("[yellow]💡 To fix: Check your Google Places API key and billing setup[/yellow]")
                console.print("[dim]   - Verify API key in config.py[/dim]")
                console.print("[dim]   - Enable Google Places API in Google Cloud Console[/dim]")
                console.print("[dim]   - Set up billing for your Google Cloud project[/dim]")
            else:
                console.print(f"[red]❌ Google Places Error: {error_msg}[/red]")
            logger.error(f"Google Places collection error: {error_msg}")
            return []
    
    def _collect_from_overpass(self, category_ids: List[str], location_data: Dict[str, Any],
                              radius_meters: int, progress: Progress, max_results: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect from Overpass API"""
        try:
            # Get Overpass amenities, shops, and offices for categories
            amenities = self.category_manager.get_overpass_amenities(category_ids)
            shops = self.category_manager.get_overpass_shops(category_ids)
            offices = self.category_manager.get_overpass_offices(category_ids)

            if not amenities and not shops and not offices:
                console.print("[yellow]No Overpass types found for selected categories[/yellow]")
                return []

            collector = OverpassAPICollector()

            # Set filters for the specific categories
            collector._set_filters(amenities, shops, offices)

            # Collect businesses with the configured filters
            businesses = collector.collect_businesses(
                lat=location_data['latitude'],
                lng=location_data['longitude'],
                radius=radius_meters
            )

            console.print(f"[green]Overpass API: Collected {len(businesses)} businesses[/green]")
            console.print(f"[dim]  - Amenities: {amenities}[/dim]")
            console.print(f"[dim]  - Shops: {shops}[/dim]")
            console.print(f"[dim]  - Offices: {offices}[/dim]")
            return businesses

        except Exception as e:
            console.print(f"[red]Error collecting from Overpass API: {str(e)}[/red]")
            logger.error(f"Overpass API collection error: {str(e)}")
            return []
    
    def _collect_from_yellow_pages(self, category_ids: List[str], location_data: Dict[str, Any],
                                  progress: Progress, max_results: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect from Yellow Pages"""
        try:
            # Get Yellow Pages search terms for categories
            search_terms = self.category_manager.get_yellow_pages_terms(category_ids)
            
            if not search_terms:
                console.print("[yellow]No Yellow Pages terms found for selected categories[/yellow]")
                return []
            
            collector = YellowPagesCollector(
                llm_provider="groq/llama-3.1-8b-instant",
                api_key=GROQ_API_KEY
            )
            
            businesses = collector.collect_businesses(
                location=location_data['name'],
                categories=search_terms,
                max_results=max_results
            )
            
            console.print(f"[green]Yellow Pages: Collected {len(businesses)} businesses[/green]")
            return businesses
            
        except Exception as e:
            console.print(f"[red]Error collecting from Yellow Pages: {str(e)}[/red]")
            logger.error(f"Yellow Pages collection error: {str(e)}")
            return []
    
    def _process_businesses(self, businesses: List[Dict[str, Any]], progress: Progress) -> List[Dict[str, Any]]:
        """Process collected businesses (filter and deduplicate)"""
        if not businesses:
            return []
        
        console.print(f"[blue]Processing {len(businesses)} collected businesses...[/blue]")
        
        # Apply filtering
        filtered_businesses = self.filter.filter_businesses(businesses)
        console.print(f"[blue]After filtering: {len(filtered_businesses)} businesses[/blue]")
        
        # Apply deduplication
        deduplicated_businesses = self.deduplicator.deduplicate_businesses(filtered_businesses)
        console.print(f"[blue]After deduplication: {len(deduplicated_businesses)} businesses[/blue]")
        
        return deduplicated_businesses
    
    def _export_results(self, businesses: List[Dict[str, Any]], output_config: Dict[str, Any],
                       progress: Progress) -> Dict[str, str]:
        """Export results to files"""
        export_paths = {}
        
        if not businesses:
            console.print("[yellow]No businesses to export[/yellow]")
            return export_paths
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = output_config.get('custom_filename', f'businesses_{timestamp}')
        
        format_choice = output_config.get('format', 'CSV')
        
        if format_choice in ['CSV', 'Both CSV and JSON']:
            csv_path = self.exporter.export_to_csv(businesses, f"{base_filename}.csv")
            if csv_path:
                export_paths['csv'] = csv_path
                console.print(f"[green]CSV exported to: {csv_path}[/green]")
        
        if format_choice in ['JSON', 'Both CSV and JSON']:
            json_path = self.exporter.export_to_json(businesses, f"{base_filename}.json")
            if json_path:
                export_paths['json'] = json_path
                console.print(f"[green]JSON exported to: {json_path}[/green]")
        
        # Always export summary report
        report_path = self.exporter.export_summary_report(businesses, f"{base_filename}_summary.txt")
        if report_path:
            export_paths['summary'] = report_path
            console.print(f"[green]Summary report exported to: {report_path}[/green]")
        
        return export_paths
    
    def _show_collection_summary(self, stats: Dict[str, Any]):
        """Show final collection summary"""
        duration = stats['duration']
        
        summary_text = f"""[bold]Collection Complete![/bold]

[bold]Results:[/bold]
• Total collected: {stats['total_collected']} businesses
• After processing: {stats['total_after_processing']} businesses
• Duration: {duration.total_seconds():.1f} seconds

[bold]Source Results:[/bold]"""

        for source, count in stats['by_source'].items():
            status = stats.get('source_status', {}).get(source, 'unknown')
            if status == 'success':
                status_icon = "✅"
                status_text = "Success"
            elif status == 'no_results':
                status_icon = "⚠️"
                status_text = "No results"
            elif status.startswith('failed'):
                status_icon = "❌"
                status_text = status.replace('failed: ', 'Failed: ')
            else:
                status_icon = "❓"
                status_text = status

            summary_text += f"\n• {source.title()}: {count} businesses {status_icon} {status_text}"
        
        if 'export_paths' in stats:
            summary_text += f"\n\n[bold]Exported Files:[/bold]"
            for file_type, path in stats['export_paths'].items():
                summary_text += f"\n• {file_type.upper()}: {path}"
        
        console.print(Panel(
            summary_text,
            title="Collection Summary",
            border_style="green"
        ))
