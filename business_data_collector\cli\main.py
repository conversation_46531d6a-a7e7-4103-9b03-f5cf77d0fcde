"""
Main CLI application for Business Data Collector
"""
import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from typing import Optional, List
import sys
import os
import questionary

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cli.commands.collect import collect_app
from cli.commands.categories import categories_app
from cli.commands.scrape import scrape_app
from cli.commands.config import config_app
from cli.commands.results import results_app

# Initialize console for rich output
console = Console()

# Create main app
app = typer.Typer(
    name="business-cli",
    help="🏢 Business Data Collector CLI - Collect business data and scrape contact information",
    rich_markup_mode="rich",
    no_args_is_help=False  # Changed to allow main menu
)

# Add subcommands
app.add_typer(collect_app, name="collect", help="📊 Collect business data from various sources")
app.add_typer(categories_app, name="categories", help="📋 Manage and view business categories")
app.add_typer(scrape_app, name="scrape", help="📧 Scrape emails and contact info from businesses")
app.add_typer(config_app, name="config", help="⚙️ Manage CLI configuration")
app.add_typer(results_app, name="results", help="📈 View and manage collection results")


def show_main_menu():
    """Display and handle the main menu interface"""
    console.print(Panel(
        Text("Business Data Collector CLI v1.0.0", style="bold blue"),
        title="🏢 Welcome to Business Data Collector",
        border_style="blue"
    ))

    while True:
        try:
            choice = questionary.select(
                "What would you like to do?",
                choices=[
                    questionary.Choice("🎯 Start Data Collection", "collect"),
                    questionary.Choice("📋 Manage Categories", "categories"),
                    questionary.Choice("📧 Scrape Emails & Contacts", "scrape"),
                    questionary.Choice("⚙️ Configuration", "config"),
                    questionary.Choice("📈 View Results", "results"),
                    questionary.Choice("❌ Exit", "exit")
                ],
                style=questionary.Style([
                    ('question', 'bold'),
                    ('answer', 'fg:#ff9d00 bold'),
                    ('pointer', 'fg:#ff9d00 bold'),
                    ('highlighted', 'fg:#ff9d00 bold'),
                    ('selected', 'fg:#cc5454'),
                    ('separator', 'fg:#cc5454'),
                    ('instruction', ''),
                    ('text', ''),
                    ('disabled', 'fg:#858585 italic')
                ])
            ).ask()

            if choice == "exit" or choice is None:
                console.print("\n[yellow]Goodbye! 👋[/yellow]")
                break
            elif choice == "collect":
                show_collection_menu()
            elif choice == "categories":
                show_categories_menu()
            elif choice == "scrape":
                show_scrape_menu()
            elif choice == "config":
                show_config_menu()
            elif choice == "results":
                show_results_menu()

        except KeyboardInterrupt:
            console.print("\n[yellow]Goodbye! 👋[/yellow]")
            break


def show_collection_menu():
    """Show collection submenu"""
    choice = questionary.select(
        "Choose collection method:",
        choices=[
            questionary.Choice("🚀 Full Pipeline (Collection + Email + Facebook)", "pipeline"),
            questionary.Choice("🎯 Interactive Collection (Data Only)", "interactive"),
            questionary.Choice("⚡ Quick Collection (Advanced)", "quick"),
            questionary.Choice("🔙 Back to Main Menu", "back")
        ]
    ).ask()

    if choice == "pipeline":
        from cli.commands.collect import full_pipeline
        full_pipeline()
    elif choice == "interactive":
        from cli.commands.collect import collect_interactive
        collect_interactive()
    elif choice == "quick":
        console.print("[yellow]Quick collection requires command-line arguments.[/yellow]")
        console.print("[dim]Use: python -m cli.main collect quick --help for options[/dim]")
    # choice == "back" or None returns to main menu


def show_categories_menu():
    """Show categories submenu"""
    choice = questionary.select(
        "Category management options:",
        choices=[
            questionary.Choice("📋 List All Categories", "list"),
            questionary.Choice("🔍 Search Categories", "search"),
            questionary.Choice("📂 Browse by Groups", "groups"),
            questionary.Choice("ℹ️ Category Information", "info"),
            questionary.Choice("🔙 Back to Main Menu", "back")
        ]
    ).ask()

    if choice == "list":
        from cli.commands.categories import list_categories
        list_categories()
    elif choice == "search":
        search_term = questionary.text("Enter search term:").ask()
        if search_term:
            from cli.commands.categories import search_categories
            search_categories(search_term)
    elif choice == "groups":
        from cli.commands.categories import list_groups
        list_groups()
    elif choice == "info":
        category = questionary.text("Enter category name or ID:").ask()
        if category:
            from cli.commands.categories import category_info
            category_info(category)


def show_scrape_menu():
    """Show scraping submenu"""
    choice = questionary.select(
        "Scraping options:",
        choices=[
            questionary.Choice("📧 Scrape Emails", "emails"),
            questionary.Choice("📞 Scrape All Contacts", "contacts"),
            questionary.Choice("📘 Extract Facebook Emails", "facebook"),
            questionary.Choice("🔙 Back to Main Menu", "back")
        ]
    ).ask()

    if choice in ["emails", "contacts"]:
        from cli.commands.scrape import interactive_scraping
        interactive_scraping()
    elif choice == "facebook":
        from cli.commands.collect import interactive_facebook_extraction
        interactive_facebook_extraction()


def show_config_menu():
    """Show configuration submenu"""
    choice = questionary.select(
        "Configuration options:",
        choices=[
            questionary.Choice("👁️ Show Current Config", "show"),
            questionary.Choice("⚙️ Set Configuration", "set"),
            questionary.Choice("🔄 Reset to Defaults", "reset"),
            questionary.Choice("🔙 Back to Main Menu", "back")
        ]
    ).ask()

    if choice == "show":
        from cli.commands.config import show_config
        show_config()
    elif choice == "set":
        from cli.commands.config import interactive_config
        interactive_config()
    elif choice == "reset":
        from cli.commands.config import reset_config
        reset_config()


def show_results_menu():
    """Show results submenu"""
    choice = questionary.select(
        "Results management:",
        choices=[
            questionary.Choice("📋 List All Results", "list"),
            questionary.Choice("👁️ Show Result Details", "show"),
            questionary.Choice("🧹 Clean Old Results", "clean"),
            questionary.Choice("🔙 Back to Main Menu", "back")
        ]
    ).ask()

    if choice == "list":
        from cli.commands.results import interactive_results
        interactive_results()
    elif choice == "show":
        from cli.commands.results import interactive_results
        interactive_results()
    elif choice == "clean":
        from cli.commands.results import interactive_results
        interactive_results()


@app.callback()
def main(
    version: Optional[bool] = typer.Option(
        None, "--version", "-v", help="Show version information"
    ),
    menu: Optional[bool] = typer.Option(
        None, "--menu", "-m", help="Show interactive main menu"
    )
):
    """
    🏢 Business Data Collector CLI

    A comprehensive tool for collecting business data and scraping contact information.
    """
    if version:
        console.print(Panel(
            Text("Business Data Collector CLI v1.0.0", style="bold blue"),
            title="Version Info",
            border_style="blue"
        ))
        raise typer.Exit()

    # If no subcommand is provided, show the main menu
    # Check if we're being called without any subcommands
    if menu:
        show_main_menu()
        raise typer.Exit()


def cli_main():
    """Entry point for the CLI application"""
    try:
        # If no arguments provided, show main menu
        if len(sys.argv) == 1:
            show_main_menu()
        else:
            app()
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error: {str(e)}[/red]")
        raise typer.Exit(1)


if __name__ == "__main__":
    cli_main()
