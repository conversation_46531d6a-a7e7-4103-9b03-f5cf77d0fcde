"""
Pittsburgh Coffee Business Collector - Find local businesses that would want morning coffee service
Focuses on Pittsburgh area only and saves data incrementally to prevent data loss
"""
import logging
import sys
import os
import json
from datetime import datetime
from typing import List, Dict, Any
import pandas as pd

# Add the business_data_collector directory to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'business_data_collector'))

from business_data_collector.collectors.google_places import GooglePlacesCollector
from business_data_collector.collectors.overpass_api import OverpassAPICollector
from business_data_collector.collectors.yellow_pages import YellowPagesCollector
from business_data_collector.utils.deduplication import BusinessDeduplicator
from business_data_collector.utils.filtering import BusinessFilter
from business_data_collector.utils.data_export import DataExporter
from business_data_collector.config import GROQ_API_KEY

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('pittsburgh_coffee_collection.log')
    ]
)

logger = logging.getLogger(__name__)

# Pittsburgh metropolitan area - main city plus surrounding towns/cities
PITTSBURGH_METRO_AREAS = [
    {"name": "Pittsburgh, PA", "lat": 40.440624, "lng": -79.995888},
    {"name": "Cranberry Township, PA", "lat": 40.6884, "lng": -80.1073},
    {"name": "Bethel Park, PA", "lat": 40.3273, "lng": -80.0387},
    {"name": "Mt. Lebanon, PA", "lat": 40.3712, "lng": -80.0431},
    {"name": "Upper St. Clair, PA", "lat": 40.3387, "lng": -80.0870},
    {"name": "Peters Township, PA", "lat": 40.3387, "lng": -80.1273},
    {"name": "Robinson Township, PA", "lat": 40.4534, "lng": -80.1387},
    {"name": "Moon Township, PA", "lat": 40.5112, "lng": -80.2087},
    {"name": "Monroeville, PA", "lat": 40.4212, "lng": -79.7881},
    {"name": "Murrysville, PA", "lat": 40.4387, "lng": -79.6534},
    {"name": "North Allegheny, PA", "lat": 40.5387, "lng": -79.9534},
    {"name": "Fox Chapel, PA", "lat": 40.5387, "lng": -79.8912},
    {"name": "Sewickley, PA", "lat": 40.5387, "lng": -80.1845},
    {"name": "McMurray, PA", "lat": 40.2912, "lng": -80.0912},
    {"name": "Wexford, PA", "lat": 40.6312, "lng": -80.0634}
]

SEARCH_RADIUS_MILES = 10  # 10 mile radius per location

# Coffee-focused business categories - office environments with employees
COFFEE_BUSINESS_CATEGORIES = {
    # Professional Services (High Priority)
    "law_firms": {
        "google_types": ["lawyer"],
        "yellow_pages_terms": ["law firms", "attorneys", "lawyers", "legal services"]
    },
    "accounting_firms": {
        "google_types": ["accounting"],
        "yellow_pages_terms": ["accounting firms", "accountants", "tax services", "bookkeeping", "CPA"]
    },
    "insurance_agencies": {
        "google_types": ["insurance_agency"],
        "yellow_pages_terms": ["insurance agencies", "insurance companies", "insurance brokers"]
    },
    "real_estate_agencies": {
        "google_types": ["real_estate_agency"],
        "yellow_pages_terms": ["real estate agencies", "realtors", "property management"]
    },
    "financial_advisors": {
        "google_types": ["accounting"],  # Closest match
        "yellow_pages_terms": ["financial advisors", "investment firms", "wealth management", "financial planning"]
    },
    
    # Medical/Health Services (High Priority)
    "medical_services": {
        "google_types": ["doctor", "hospital"],
        "yellow_pages_terms": ["medical services", "doctors", "healthcare", "clinics"]
    },
    "dentists": {
        "google_types": ["dentist"],
        "yellow_pages_terms": ["dentists", "dental services", "dental offices"]
    },
    "veterinary": {
        "google_types": ["veterinary_care"],
        "yellow_pages_terms": ["veterinary", "animal hospitals", "vet clinics"]
    },
    
    # Financial Services
    "banks": {
        "google_types": ["bank"],
        "yellow_pages_terms": ["banks", "credit unions", "banking services"]
    },
    
    # Government/Public Services
    "government_offices": {
        "google_types": ["government_office", "city_hall", "local_government_office"],
        "yellow_pages_terms": ["government offices", "city hall", "public services"]
    },
    
    # Education
    "schools": {
        "google_types": ["school", "primary_school", "secondary_school"],
        "yellow_pages_terms": ["schools", "elementary schools", "high schools", "private schools"]
    },
    "universities": {
        "google_types": ["university"],
        "yellow_pages_terms": ["universities", "colleges", "higher education"]
    },
    
    # Additional Business Types
    "marketing_agencies": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["marketing agencies", "advertising agencies", "digital marketing"]
    },
    "it_companies": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["IT companies", "software companies", "technology companies"]
    },
    "consulting_firms": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["consulting firms", "business consultants", "management consulting"]
    },
    "architecture_firms": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["architecture firms", "architects", "architectural services"]
    },
    "engineering_firms": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["engineering firms", "engineers", "engineering services"]
    }
}

class PittsburghCoffeeCollector:
    """Specialized collector for Pittsburgh businesses that would want coffee service"""
    
    def __init__(self):
        self.google_collector = GooglePlacesCollector()
        self.overpass_collector = OverpassAPICollector()
        self.yellow_pages_collector = YellowPagesCollector(
            llm_provider="groq/llama-3.1-8b-instant",
            api_key=GROQ_API_KEY
        )
        self.deduplicator = BusinessDeduplicator()
        self.filter = BusinessFilter()
        self.exporter = DataExporter()

        # Create timestamp for this collection session
        self.session_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create output directory
        self.output_dir = "9-14-output"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def collect_pittsburgh_coffee_businesses(self) -> List[Dict[str, Any]]:
        """
        Collect businesses from Pittsburgh metro area that would want coffee service
        Saves data incrementally to prevent data loss
        """
        logger.info("=" * 60)
        logger.info("PITTSBURGH METRO COFFEE BUSINESS COLLECTION STARTED")
        logger.info("=" * 60)
        logger.info(f"Session ID: {self.session_timestamp}")
        logger.info(f"Areas to search: {len(PITTSBURGH_METRO_AREAS)} locations")
        logger.info(f"Radius per location: {SEARCH_RADIUS_MILES} miles")

        all_businesses = []

        # Step 1: Collect from Google Places API (all areas)
        logger.info("\n--- STEP 1: Google Places API Collection ---")
        google_businesses = self._collect_google_places_all_areas()
        all_businesses.extend(google_businesses)
        self._save_incremental_data(google_businesses, "google_places")
        logger.info(f"Google Places: {len(google_businesses)} businesses collected and saved")

        # Step 2: Collect from Overpass API (all areas)
        logger.info("\n--- STEP 2: Overpass API Collection ---")
        overpass_businesses = self._collect_overpass_api_all_areas()
        all_businesses.extend(overpass_businesses)
        self._save_incremental_data(overpass_businesses, "overpass_api")
        logger.info(f"Overpass API: {len(overpass_businesses)} businesses collected and saved")

        # Step 3: Collect from Yellow Pages (all areas)
        logger.info("\n--- STEP 3: Yellow Pages Collection ---")
        yellow_pages_businesses = self._collect_yellow_pages_all_areas()
        all_businesses.extend(yellow_pages_businesses)
        self._save_incremental_data(yellow_pages_businesses, "yellow_pages")
        logger.info(f"Yellow Pages: {len(yellow_pages_businesses)} businesses collected and saved")

        logger.info(f"\nTotal raw businesses collected: {len(all_businesses)}")

        # Step 4: Process and deduplicate all data
        logger.info("\n--- STEP 4: Processing and Deduplication ---")
        processed_businesses = self._process_businesses(all_businesses)

        # Step 5: Export final results
        logger.info("\n--- STEP 5: Final Export ---")
        self._export_final_results(processed_businesses)

        return processed_businesses
    
    def _collect_google_places_all_areas(self) -> List[Dict[str, Any]]:
        """Collect from Google Places API for all Pittsburgh metro areas"""
        all_businesses = []

        for area_idx, area in enumerate(PITTSBURGH_METRO_AREAS, 1):
            logger.info(f"  Collecting from {area['name']} ({area_idx}/{len(PITTSBURGH_METRO_AREAS)})...")
            area_businesses = self._collect_google_places_single_area(area)

            # Add area information to each business
            for business in area_businesses:
                business['search_area'] = area['name']
                business['search_coordinates'] = f"{area['lat']}, {area['lng']}"

            all_businesses.extend(area_businesses)
            logger.info(f"    {area['name']}: {len(area_businesses)} businesses")

        return all_businesses

    def _collect_google_places_single_area(self, area: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect from Google Places API for a single area"""
        businesses = []

        for category_name, category_config in COFFEE_BUSINESS_CATEGORIES.items():
            if 'google_types' not in category_config:
                continue

            try:
                category_businesses = self.google_collector.collect_businesses(
                    lat=area['lat'],
                    lng=area['lng'],
                    business_types=category_config['google_types'],
                    max_results=20  # Reasonable limit per category per area
                )

                # Add category and source information
                for business in category_businesses:
                    business['coffee_category'] = category_name
                    business['collection_session'] = self.session_timestamp

                businesses.extend(category_businesses)

            except Exception as e:
                logger.error(f"      Error collecting {category_name} from {area['name']}: {str(e)}")

        return businesses
    
    def _collect_overpass_api_all_areas(self) -> List[Dict[str, Any]]:
        """Collect from Overpass API for all Pittsburgh metro areas"""
        all_businesses = []

        for area_idx, area in enumerate(PITTSBURGH_METRO_AREAS, 1):
            logger.info(f"  Collecting from {area['name']} ({area_idx}/{len(PITTSBURGH_METRO_AREAS)})...")
            area_businesses = self._collect_overpass_api_single_area(area)

            # Add area information to each business
            for business in area_businesses:
                business['search_area'] = area['name']
                business['search_coordinates'] = f"{area['lat']}, {area['lng']}"

            all_businesses.extend(area_businesses)
            logger.info(f"    {area['name']}: {len(area_businesses)} businesses")

        return all_businesses

    def _collect_overpass_api_single_area(self, area: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect from Overpass API for a single area"""
        try:
            businesses = self.overpass_collector.collect_businesses(
                lat=area['lat'],
                lng=area['lng']
            )

            # Filter for coffee-relevant business types and add metadata
            coffee_businesses = []
            for business in businesses:
                business_type = business.get('type', '').lower()
                amenity = business.get('amenity', '').lower()

                # Check if this business type is relevant for coffee service
                if any([
                    'office' in business_type,
                    'bank' in amenity,
                    'hospital' in amenity,
                    'clinic' in amenity,
                    'dentist' in amenity,
                    'veterinary' in amenity,
                    'school' in amenity,
                    'university' in amenity,
                    'government' in business_type
                ]):
                    business['coffee_category'] = 'office_building'
                    business['collection_session'] = self.session_timestamp
                    coffee_businesses.append(business)

            return coffee_businesses

        except Exception as e:
            logger.error(f"Error collecting from Overpass API for {area['name']}: {str(e)}")
            return []
    
    def _collect_yellow_pages_all_areas(self) -> List[Dict[str, Any]]:
        """Collect from Yellow Pages for all Pittsburgh metro areas"""
        all_businesses = []

        for area_idx, area in enumerate(PITTSBURGH_METRO_AREAS, 1):
            logger.info(f"  Collecting from {area['name']} ({area_idx}/{len(PITTSBURGH_METRO_AREAS)})...")
            area_businesses = self._collect_yellow_pages_single_area(area)

            # Add area information to each business
            for business in area_businesses:
                business['search_area'] = area['name']

            all_businesses.extend(area_businesses)
            logger.info(f"    {area['name']}: {len(area_businesses)} businesses")

        return all_businesses

    def _collect_yellow_pages_single_area(self, area: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect from Yellow Pages for a single area"""
        businesses = []

        for category_name, category_config in COFFEE_BUSINESS_CATEGORIES.items():
            if 'yellow_pages_terms' not in category_config:
                continue

            try:
                category_businesses = self.yellow_pages_collector.collect_businesses(
                    location=area['name'],
                    categories=category_config['yellow_pages_terms'],
                    max_results=50  # Reasonable limit per category per area
                )

                # Add category and source information
                for business in category_businesses:
                    business['coffee_category'] = category_name
                    business['collection_session'] = self.session_timestamp

                businesses.extend(category_businesses)

            except Exception as e:
                logger.error(f"      Error collecting {category_name} from {area['name']}: {str(e)}")

        return businesses
    
    def _save_incremental_data(self, businesses: List[Dict[str, Any]], source_name: str) -> None:
        """Save data incrementally after each collection step"""
        if not businesses:
            logger.warning(f"No businesses to save for {source_name}")
            return

        # Save as JSON for intermediate storage
        json_filename = os.path.join(self.output_dir, f"pittsburgh_coffee_{source_name}_{self.session_timestamp}.json")
        try:
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(businesses, f, indent=2, ensure_ascii=False, default=str)
            logger.info(f"  Incremental data saved: {json_filename}")

            # Also save as CSV for immediate viewing
            csv_filename = os.path.join(self.output_dir, f"pittsburgh_coffee_{source_name}_{self.session_timestamp}.csv")
            df = pd.DataFrame(businesses)
            df.to_csv(csv_filename, index=False, encoding='utf-8')
            logger.info(f"  CSV saved: {csv_filename}")

        except Exception as e:
            logger.error(f"Error saving incremental data for {source_name}: {str(e)}")
    
    def _process_businesses(self, businesses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process collected business data (filter and deduplicate)"""
        logger.info(f"Processing {len(businesses)} collected businesses...")
        
        if not businesses:
            logger.warning("No businesses to process")
            return []
        
        # Add collection timestamp
        timestamp = datetime.now().isoformat()
        for business in businesses:
            business['collected_at'] = timestamp
            business['collection_purpose'] = 'pittsburgh_coffee_service_prospects'
        
        # Filter out corporate chains and invalid businesses
        logger.info("Filtering businesses...")
        filtered_businesses = self.filter.filter_businesses(businesses)
        logger.info(f"After filtering: {len(filtered_businesses)} businesses")
        
        # Deduplicate businesses
        logger.info("Deduplicating businesses...")
        unique_businesses = self.deduplicator.deduplicate_businesses(filtered_businesses)
        logger.info(f"After deduplication: {len(unique_businesses)} unique businesses")
        
        return unique_businesses
    
    def _export_final_results(self, businesses: List[Dict[str, Any]]) -> None:
        """Export final processed business data to CSV"""
        logger.info("Exporting final results...")
        
        if not businesses:
            logger.warning("No businesses to export")
            return
        
        # Generate final filename
        filename = os.path.join(self.output_dir, f"pittsburgh_coffee_prospects_FINAL_{self.session_timestamp}.csv")

        try:
            # Export to CSV using pandas for better control
            df = pd.DataFrame(businesses)
            df.to_csv(filename, index=False, encoding='utf-8')
            logger.info(f"Final CSV exported: {filename}")
            print(f"\n✅ Final results exported to: {filename}")
            
            # Print summary statistics
            self._print_summary_stats(businesses)
            
        except Exception as e:
            logger.error(f"Error exporting final results: {str(e)}")
    
    def _print_summary_stats(self, businesses: List[Dict[str, Any]]) -> None:
        """Print summary statistics about collected businesses"""
        print(f"\n📊 PITTSBURGH COFFEE BUSINESS COLLECTION SUMMARY")
        print(f"=" * 60)
        print(f"Total unique businesses: {len(businesses)}")
        
        # Breakdown by category
        categories = {}
        sources = {}
        
        for business in businesses:
            # Category breakdown
            category = business.get('coffee_category', 'unknown')
            categories[category] = categories.get(category, 0) + 1
            
            # Source breakdown
            source = business.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        print(f"\nBreakdown by business category:")
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"  {category}: {count}")
        
        print(f"\nBreakdown by data source:")
        for source, count in sorted(sources.items(), key=lambda x: x[1], reverse=True):
            print(f"  {source}: {count}")


def main():
    """Main function to run the Pittsburgh coffee business collection"""
    try:
        collector = PittsburghCoffeeCollector()
        
        print(f"🚀 Starting Pittsburgh Metro Coffee Business Collection")
        print(f"📍 Areas: {len(PITTSBURGH_METRO_AREAS)} locations in Pittsburgh metro area")
        print(f"📏 Radius: {SEARCH_RADIUS_MILES} miles per location")
        print(f"💾 Data will be saved incrementally to prevent loss")
        print(f"⏱️  Session ID: {collector.session_timestamp}")
        print(f"🏙️  Areas to search: {', '.join([area['name'] for area in PITTSBURGH_METRO_AREAS[:5]])}{'...' if len(PITTSBURGH_METRO_AREAS) > 5 else ''}")
        
        businesses = collector.collect_pittsburgh_coffee_businesses()
        
        print(f"\n🎉 Collection completed successfully!")
        print(f"📈 Found {len(businesses)} unique Pittsburgh coffee business prospects")
        
    except KeyboardInterrupt:
        logger.info("Collection interrupted by user")
        print("\n❌ Collection interrupted by user")
        print("💾 Check for incremental data files that were saved during collection")
    except Exception as e:
        logger.error(f"Unexpected error during collection: {str(e)}")
        print(f"\n❌ Error during collection: {str(e)}")
        print("💾 Check for incremental data files that were saved during collection")
        sys.exit(1)


if __name__ == "__main__":
    main()
