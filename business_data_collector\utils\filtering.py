"""
Filtering utilities for business data.
"""
import logging
from typing import List, Dict, Any
import re
from config import CORPORATE_BLACKLIST

logger = logging.getLogger(__name__)


class BusinessFilter:
    """Handles filtering of business data to exclude corporate chains."""
    
    def __init__(self, corporate_blacklist: List[str] = None):
        """Initialize the business filter.
        
        Args:
            corporate_blacklist: List of corporate names to exclude
        """
        self.corporate_blacklist = corporate_blacklist or CORPORATE_BLACKLIST
        # Normalize blacklist for better matching
        self.normalized_blacklist = [self._normalize_name(name) for name in self.corporate_blacklist]
        
    def filter_businesses(self, businesses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter out corporate chains and invalid businesses.
        
        Args:
            businesses: List of business data dictionaries
            
        Returns:
            List of filtered business data dictionaries
        """
        if not businesses:
            return []
            
        logger.info(f"Starting filtering of {len(businesses)} businesses...")
        
        filtered_businesses = []
        
        for business in businesses:
            if self._should_include_business(business):
                filtered_businesses.append(business)
            else:
                logger.debug(f"Filtered out: {business.get('name', 'Unknown')}")
                
        logger.info(f"After filtering: {len(filtered_businesses)} businesses remain")
        return filtered_businesses
    
    def _should_include_business(self, business: Dict[str, Any]) -> bool:
        """Determine if a business should be included in results.
        
        Args:
            business: Business data dictionary
            
        Returns:
            True if business should be included, False otherwise
        """
        # Check if business has required fields
        if not self._has_required_fields(business):
            return False
            
        # Check if business is a corporate chain
        if self._is_corporate_chain(business):
            return False
            
        # Check if business appears to be valid
        if not self._is_valid_business(business):
            return False
            
        return True
    
    def _has_required_fields(self, business: Dict[str, Any]) -> bool:
        """Check if business has minimum required fields.
        
        Args:
            business: Business data dictionary
            
        Returns:
            True if business has required fields, False otherwise
        """
        name = business.get('name', '').strip()
        
        # Must have a name
        if not name:
            return False
            
        # Must have at least one contact method (address, phone, or website)
        address = business.get('address', '').strip()
        phone = business.get('phone', '').strip()
        website = business.get('website', '').strip()
        
        if not any([address, phone, website]):
            return False
            
        return True
    
    def _is_corporate_chain(self, business: Dict[str, Any]) -> bool:
        """Check if business is a corporate chain.
        
        Args:
            business: Business data dictionary
            
        Returns:
            True if business is a corporate chain, False otherwise
        """
        name = business.get('name', '').strip()
        if not name:
            return False
            
        normalized_name = self._normalize_name(name)
        
        # Check against blacklist
        for blacklisted_name in self.normalized_blacklist:
            if self._names_match(normalized_name, blacklisted_name):
                return True
                
        # Check for common corporate indicators
        corporate_indicators = [
            r'\b(franchise|franchisee)\b',
            r'\b(chain|chains)\b',
            r'\b(corporate|corp)\b',
            r'\b(national|nationwide)\b',
            r'\b(international|intl)\b',
            r'\b(holdings|holding)\b',
            r'\b(enterprises|enterprise)\b',
            r'\b(group|groups)\b',
            r'\b(brands|brand)\b',
            r'\b(systems|system)\b'
        ]
        
        for indicator in corporate_indicators:
            if re.search(indicator, normalized_name, re.IGNORECASE):
                # Additional check: if it's a small local business with these words, keep it
                if not self._appears_local(business):
                    return True
                    
        return False
    
    def _is_valid_business(self, business: Dict[str, Any]) -> bool:
        """Check if business appears to be a valid business.
        
        Args:
            business: Business data dictionary
            
        Returns:
            True if business appears valid, False otherwise
        """
        name = business.get('name', '').strip()
        if not name:
            return False
            
        # Filter out obvious non-businesses
        invalid_patterns = [
            r'^(test|testing|example|sample)\b',
            r'^(temp|temporary|placeholder)\b',
            r'^(unknown|unnamed|untitled)\b',
            r'^(private|personal|individual)\b',
            r'^(home|house|residence)\b',
            r'^(apartment|apt|unit)\b',
            r'^(parking|lot|garage)\b',
            r'^(atm|automated teller)\b',
            r'^(restroom|bathroom|toilet)\b',
            r'^(entrance|exit|door)\b',
            r'^(sign|signage|billboard)\b'
        ]
        
        normalized_name = self._normalize_name(name)
        
        for pattern in invalid_patterns:
            if re.search(pattern, normalized_name, re.IGNORECASE):
                return False
                
        # Check for minimum name length
        if len(normalized_name) < 2:
            return False
            
        # Check for suspicious patterns (all caps, all numbers, etc.)
        if normalized_name.isupper() and len(normalized_name) > 20:
            return False
            
        if normalized_name.isdigit():
            return False
            
        return True
    
    def _normalize_name(self, name: str) -> str:
        """Normalize business name for comparison.

        Args:
            name: Business name to normalize

        Returns:
            Normalized business name
        """
        if not name:
            return ''

        # Convert to lowercase
        name = name.lower()

        # Remove common prefixes and suffixes
        prefixes = [r'^(the|a|an)\s+']
        suffixes = [
            r'\s+(inc|llc|corp|corporation|company|co|ltd|limited)\.?$',
            r'\s+(restaurant|cafe|bar|pub|grill|diner|eatery)\.?$',
            r'\s+(shop|store|market|mart|center|centre)\.?$',
            r'\s+(service|services|solutions)\.?$',
            r'\s+(group|associates|partners)\.?$'
        ]

        for prefix in prefixes:
            name = re.sub(prefix, '', name)

        for suffix in suffixes:
            name = re.sub(suffix, '', name)

        # Remove special characters and normalize spaces (keep apostrophes for now)
        name = re.sub(r'[^\w\s\']', ' ', name)
        name = re.sub(r'\s+', ' ', name)

        return name.strip()
    
    def _names_match(self, name1: str, name2: str) -> bool:
        """Check if two normalized names match.

        Args:
            name1: First normalized name
            name2: Second normalized name

        Returns:
            True if names match, False otherwise
        """
        if not name1 or not name2:
            return False

        # Remove apostrophes for comparison
        clean_name1 = name1.replace("'", "")
        clean_name2 = name2.replace("'", "")

        # Exact match
        if clean_name1 == clean_name2:
            return True

        # Check if one name contains the other (for partial matches)
        if len(clean_name2) >= 4:  # Minimum length for partial matching
            if clean_name2 in clean_name1 or clean_name1 in clean_name2:
                return True

        return False
    
    def _appears_local(self, business: Dict[str, Any]) -> bool:
        """Check if business appears to be a local business.
        
        Args:
            business: Business data dictionary
            
        Returns:
            True if business appears local, False otherwise
        """
        # Check for local indicators in name
        name = business.get('name', '').lower()
        local_indicators = [
            'pittsburgh', 'pgh', 'steel city', 'burgh',
            'local', 'neighborhood', 'family', 'mom and pop',
            'independent', 'locally owned', 'community'
        ]
        
        for indicator in local_indicators:
            if indicator in name:
                return True
                
        # Check address for local area codes or zip codes
        address = business.get('address', '').lower()
        if any(zip_code in address for zip_code in ['15201', '15202', '15203', '15204', '15205']):
            return True
            
        # Check phone for local area codes
        phone = business.get('phone', '')
        if any(area_code in phone for area_code in ['412', '724', '878']):
            return True
            
        return False
