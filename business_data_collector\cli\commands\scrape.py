"""
Email scraping commands for the CLI
"""
import typer
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.table import Table
from typing import Optional, List
import pandas as pd
import asyncio
import sys
import os
from datetime import datetime
import questionary

# Add paths for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
aganl_path = os.path.join(project_root, 'aganl')

sys.path.append(project_root)
sys.path.append(aganl_path)

try:
    from perfect_contact_extractor import PerfectContactExtractor
    SCRAPING_AVAILABLE = True
except ImportError as e:
    console = Console()
    console.print(f"[yellow]Warning: Could not import PerfectContactExtractor: {e}[/yellow]")
    console.print(f"[yellow]Scraping functionality will be limited. Make sure aganl module is available at: {aganl_path}[/yellow]")
    PerfectContactExtractor = None
    SCRAPING_AVAILABLE = False

console = Console()

# Create scrape app
scrape_app = typer.Typer(help="Scrape emails and contact info from businesses")


@scrape_app.command("emails")
def scrape_emails(
    input_file: str = typer.Argument(..., help="CSV file with business data"),
    output_file: Optional[str] = typer.Option(
        None, "--output", "-o", help="Output file for scraped emails"
    ),
    batch_size: int = typer.Option(
        25, "--batch-size", "-b", help="Number of URLs to process per batch"
    ),
    max_concurrent: int = typer.Option(
        5, "--concurrent", "-c", help="Maximum concurrent requests per batch"
    ),
    website_column: str = typer.Option(
        "website", "--website-col", help="Column name containing website URLs"
    ),
    amount: Optional[int] = typer.Option(
        None, "--amount", "-a", help="Limit number of URLs to process (for testing)"
    ),
    limit: Optional[int] = typer.Option(
        None, "--limit", "-l", help="Limit number of URLs to process (for testing)"
    )
):
    """
    📧 Scrape emails from business websites
    """
    if not SCRAPING_AVAILABLE:
        console.print("[red]Error: Email scraping functionality not available[/red]")
        console.print("[yellow]Make sure the aganl module is properly installed[/yellow]")
        raise typer.Exit(1)

    console.print(Panel(
        f"Scraping emails from businesses in: {input_file}\n"
        f"Batch size: {batch_size}, Concurrent: {max_concurrent}",
        title="Email Scraping",
        border_style="cyan"
    ))

    # Run the async scraping
    asyncio.run(_scrape_emails_async(input_file, output_file, batch_size, max_concurrent, website_column, amount))


@scrape_app.command("contacts")
def scrape_contacts(
    input_file: str = typer.Argument(..., help="CSV file with business data"),
    output_file: Optional[str] = typer.Option(
        None, "--output", "-o", help="Output file for scraped contacts"
    ),
    batch_size: int = typer.Option(
        25, "--batch-size", "-b", help="Number of URLs to process per batch"
    ),
    max_concurrent: int = typer.Option(
        5, "--concurrent", "-c", help="Maximum concurrent requests per batch"
    ),
    website_column: str = typer.Option(
        "website", "--website-col", help="Column name containing website URLs"
    ),
    amount: Optional[int] = typer.Option(
        None, "--amount", "-a", help="Limit number of URLs to process (for testing)"
    )
):
    """
    📞 Scrape all contact information from business websites
    """
    if not SCRAPING_AVAILABLE:
        console.print("[red]Error: Contact scraping functionality not available[/red]")
        console.print("[yellow]Make sure the aganl module is properly installed[/yellow]")
        raise typer.Exit(1)

    console.print(Panel(
        f"Scraping contacts from businesses in: {input_file}\n"
        f"Batch size: {batch_size}, Concurrent: {max_concurrent}",
        title="Contact Scraping",
        border_style="cyan"
    ))

    # Run the async scraping
    asyncio.run(_scrape_contacts_async(input_file, output_file, batch_size, max_concurrent, website_column, amount))


async def _scrape_emails_async(input_file: str, output_file: Optional[str],
                              batch_size: int, max_concurrent: int, website_column: str, amount: Optional[int] = None):
    """Async function to scrape emails from business websites"""
    try:
        # Load business data
        console.print("[blue]Loading business data...[/blue]")

        if not os.path.exists(input_file):
            console.print(f"[red]Error: Input file '{input_file}' not found[/red]")
            return

        df = pd.read_csv(input_file)

        if website_column not in df.columns:
            console.print(f"[red]Error: Column '{website_column}' not found in CSV[/red]")
            console.print(f"[yellow]Available columns: {', '.join(df.columns)}[/yellow]")
            return

        # Filter out rows without websites
        df_with_websites = df[df[website_column].notna() & (df[website_column] != '')]

        if len(df_with_websites) == 0:
            console.print("[yellow]No businesses with websites found in the data[/yellow]")
            return

        console.print(f"[green]Found {len(df_with_websites)} businesses with websites[/green]")

        # Limit the number of URLs if amount is specified
        if amount and amount < len(df_with_websites):
            df_with_websites = df_with_websites.head(amount)
            console.print(f"[yellow]Limiting to first {amount} businesses for processing[/yellow]")

        # Extract URLs
        urls = df_with_websites[website_column].tolist()

        # Initialize extractor
        extractor = PerfectContactExtractor(batch_size=batch_size, max_concurrent=max_concurrent)

        # Scrape emails
        console.print(f"[blue]Starting email extraction from {len(urls)} websites...[/blue]")
        results = await extractor.extract_perfect(urls)

        # Process results
        email_results = []
        for i, result in enumerate(results):
            business_data = df_with_websites.iloc[i].to_dict()

            if 'error' not in result:
                email_info = result.get('email')
                phone_info = result.get('phone')
                social_info = result.get('social_media')

                email_results.append({
                    **business_data,
                    'scraped_email': email_info.get('email', '') if email_info else '',
                    'email_confidence': email_info.get('confidence', 0) if email_info else 0,
                    'scraped_phone': phone_info.get('phone', '') if phone_info else '',
                    'phone_confidence': phone_info.get('confidence', 0) if phone_info else 0,
                    'social_platform': social_info.get('platform', '') if social_info else '',
                    'social_handle': social_info.get('handle', '') if social_info else '',
                    'social_url': social_info.get('url', '') if social_info else '',
                    'social_confidence': social_info.get('confidence', 0) if social_info else 0,
                    'pages_checked': result.get('pages_checked', 0),
                    'scrape_timestamp': datetime.now().isoformat()
                })
            else:
                email_results.append({
                    **business_data,
                    'scraped_email': '',
                    'email_confidence': 0,
                    'scraped_phone': '',
                    'phone_confidence': 0,
                    'social_platform': '',
                    'social_handle': '',
                    'social_url': '',
                    'social_confidence': 0,
                    'pages_checked': 0,
                    'scrape_error': result.get('error', ''),
                    'scrape_timestamp': datetime.now().isoformat()
                })

        # Save results
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"scraped_emails_{timestamp}.csv"

        results_df = pd.DataFrame(email_results)
        results_df.to_csv(output_file, index=False)

        # Show summary
        emails_found = len([r for r in email_results if r.get('scraped_email')])
        phones_found = len([r for r in email_results if r.get('scraped_phone')])
        social_found = len([r for r in email_results if r.get('social_platform')])

        console.print(Panel(
            f"[bold]Contact Scraping Complete![/bold]\n\n"
            f"• Total businesses processed: {len(results)}\n"
            f"• Emails found: {emails_found} ({emails_found/len(results)*100:.1f}%)\n"
            f"• Phone numbers found: {phones_found} ({phones_found/len(results)*100:.1f}%)\n"
            f"• Social media found: {social_found} ({social_found/len(results)*100:.1f}%)\n"
            f"• Results saved to: {output_file}",
            title="Contact Extraction Summary",
            border_style="green"
        ))

    except Exception as e:
        console.print(f"[red]Error during email scraping: {str(e)}[/red]")


async def _scrape_contacts_async(input_file: str, output_file: Optional[str],
                                batch_size: int, max_concurrent: int, website_column: str, amount: Optional[int] = None):
    """Async function to scrape all contacts from business websites"""
    try:
        # Load business data
        console.print("[blue]Loading business data...[/blue]")

        if not os.path.exists(input_file):
            console.print(f"[red]Error: Input file '{input_file}' not found[/red]")
            return

        df = pd.read_csv(input_file)

        if website_column not in df.columns:
            console.print(f"[red]Error: Column '{website_column}' not found in CSV[/red]")
            console.print(f"[yellow]Available columns: {', '.join(df.columns)}[/yellow]")
            return

        # Filter out rows without websites
        df_with_websites = df[df[website_column].notna() & (df[website_column] != '')]

        if len(df_with_websites) == 0:
            console.print("[yellow]No businesses with websites found in the data[/yellow]")
            return

        console.print(f"[green]Found {len(df_with_websites)} businesses with websites[/green]")

        # Limit the number of URLs if amount is specified
        if amount and amount < len(df_with_websites):
            df_with_websites = df_with_websites.head(amount)
            console.print(f"[yellow]Limiting to first {amount} businesses for processing[/yellow]")

        # Extract URLs
        urls = df_with_websites[website_column].tolist()

        # Initialize extractor
        extractor = PerfectContactExtractor(batch_size=batch_size, max_concurrent=max_concurrent)

        # Scrape contacts
        console.print(f"[blue]Starting contact extraction from {len(urls)} websites...[/blue]")
        results = await extractor.extract_perfect(urls)

        # Process results
        contact_results = []
        for i, result in enumerate(results):
            business_data = df_with_websites.iloc[i].to_dict()

            if 'error' not in result:
                email_info = result.get('email')
                social_info = result.get('social_media')

                contact_results.append({
                    **business_data,
                    'scraped_email': email_info.get('email', '') if email_info else '',
                    'email_confidence': email_info.get('confidence', 0) if email_info else 0,
                    'social_platform': social_info.get('platform', '') if social_info else '',
                    'social_handle': social_info.get('handle', '') if social_info else '',
                    'social_url': social_info.get('url', '') if social_info else '',
                    'pages_checked': result.get('pages_checked', 0),
                    'scrape_timestamp': datetime.now().isoformat()
                })
            else:
                contact_results.append({
                    **business_data,
                    'scraped_email': '',
                    'email_confidence': 0,
                    'social_platform': '',
                    'social_handle': '',
                    'social_url': '',
                    'pages_checked': 0,
                    'scrape_error': result.get('error', ''),
                    'scrape_timestamp': datetime.now().isoformat()
                })

        # Save results
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"scraped_contacts_{timestamp}.csv"

        results_df = pd.DataFrame(contact_results)
        results_df.to_csv(output_file, index=False)

        # Show summary
        emails_found = len([r for r in contact_results if r.get('scraped_email')])
        socials_found = len([r for r in contact_results if r.get('social_platform')])

        console.print(Panel(
            f"[bold]Contact Scraping Complete![/bold]\n\n"
            f"• Total businesses processed: {len(results)}\n"
            f"• Emails found: {emails_found}\n"
            f"• Social media found: {socials_found}\n"
            f"• Email success rate: {emails_found/len(results)*100:.1f}%\n"
            f"• Social success rate: {socials_found/len(results)*100:.1f}%\n"
            f"• Results saved to: {output_file}",
            title="Scraping Summary",
            border_style="green"
        ))

    except Exception as e:
        console.print(f"[red]Error during contact scraping: {str(e)}[/red]")


@scrape_app.command("interactive")
def interactive_scraping():
    """
    🎯 Interactive email and contact scraping
    """
    if not SCRAPING_AVAILABLE:
        console.print("[red]Error: Scraping functionality not available[/red]")
        console.print("[yellow]Make sure the aganl module is properly installed[/yellow]")
        raise typer.Exit(1)

    console.print(Panel(
        "📧 Interactive Scraping\n\n"
        "Extract emails and contact information from business websites using an easy-to-use interface.",
        title="Email & Contact Scraping",
        border_style="cyan"
    ))

    while True:
        choice = questionary.select(
            "What would you like to do?",
            choices=[
                questionary.Choice("📧 Scrape Emails Only", "emails"),
                questionary.Choice("📞 Scrape All Contacts (Emails + Social)", "contacts"),
                questionary.Choice("⚙️ Configure Scraping Settings", "settings"),
                questionary.Choice("✅ Done", "done")
            ],
            style=questionary.Style([
                ('question', 'bold'),
                ('answer', 'fg:#ff9d00 bold'),
                ('pointer', 'fg:#ff9d00 bold'),
                ('highlighted', 'fg:#ff9d00 bold'),
            ])
        ).ask()

        if choice == "emails":
            _interactive_email_scraping()
        elif choice == "contacts":
            _interactive_contact_scraping()
        elif choice == "settings":
            _configure_scraping_settings()
        elif choice == "done" or choice is None:
            break


def _interactive_email_scraping():
    """Interactive email scraping workflow"""
    console.print(Panel(
        "📧 Email Scraping Workflow\n\n"
        "Select a CSV file containing business data to extract emails from their websites.",
        title="Email Scraping",
        border_style="blue"
    ))

    # Step 1: Select CSV file
    console.print("[bold blue]Step 1: Select CSV File[/bold blue]")
    from cli.menu_helpers import browse_csv_files

    csv_file = browse_csv_files()
    if not csv_file:
        console.print("[yellow]No file selected. Returning to menu.[/yellow]")
        return

    # Step 2: Configure scraping parameters
    console.print("\n[bold blue]Step 2: Configure Scraping Parameters[/bold blue]")
    scraping_config = _get_scraping_configuration(csv_file)

    if not scraping_config:
        console.print("[yellow]Configuration cancelled. Returning to menu.[/yellow]")
        return

    # Step 3: Confirm and start scraping
    console.print("\n[bold blue]Step 3: Review and Confirm[/bold blue]")
    if _confirm_scraping_config(csv_file, scraping_config, "emails"):
        console.print("\n[green]Starting email scraping...[/green]")
        asyncio.run(_scrape_emails_async(
            csv_file,
            scraping_config['output_file'],
            scraping_config['batch_size'],
            scraping_config['max_concurrent'],
            scraping_config['website_column'],
            scraping_config['amount']
        ))


def _interactive_contact_scraping():
    """Interactive contact scraping workflow"""
    console.print(Panel(
        "📞 Contact Scraping Workflow\n\n"
        "Select a CSV file containing business data to extract emails and social media from their websites.",
        title="Contact Scraping",
        border_style="blue"
    ))

    # Step 1: Select CSV file
    console.print("[bold blue]Step 1: Select CSV File[/bold blue]")
    from cli.menu_helpers import browse_csv_files

    csv_file = browse_csv_files()
    if not csv_file:
        console.print("[yellow]No file selected. Returning to menu.[/yellow]")
        return

    # Step 2: Configure scraping parameters
    console.print("\n[bold blue]Step 2: Configure Scraping Parameters[/bold blue]")
    scraping_config = _get_scraping_configuration(csv_file)

    if not scraping_config:
        console.print("[yellow]Configuration cancelled. Returning to menu.[/yellow]")
        return

    # Step 3: Confirm and start scraping
    console.print("\n[bold blue]Step 3: Review and Confirm[/bold blue]")
    if _confirm_scraping_config(csv_file, scraping_config, "contacts"):
        console.print("\n[green]Starting contact scraping...[/green]")
        asyncio.run(_scrape_contacts_async(
            csv_file,
            scraping_config['output_file'],
            scraping_config['batch_size'],
            scraping_config['max_concurrent'],
            scraping_config['website_column'],
            scraping_config['amount']
        ))


def _get_scraping_configuration(csv_file: str) -> Optional[dict]:
    """Get scraping configuration from user"""
    try:
        # Load CSV to check columns and data
        df = pd.read_csv(csv_file)

        console.print(f"[green]Loaded CSV with {len(df)} rows and {len(df.columns)} columns[/green]")

        # Step 1: Select website column
        website_columns = [col for col in df.columns if 'website' in col.lower() or 'url' in col.lower()]

        if not website_columns:
            # Show all columns if no obvious website columns
            website_columns = list(df.columns)

        website_column = questionary.select(
            "Select the column containing website URLs:",
            choices=website_columns + ["📝 Enter custom column name"]
        ).ask()

        if website_column == "📝 Enter custom column name":
            website_column = questionary.text(
                "Enter column name:",
                validate=lambda x: x in df.columns or f"Column '{x}' not found in CSV"
            ).ask()

        if not website_column or website_column not in df.columns:
            console.print("[red]Invalid column selection[/red]")
            return None

        # Check how many rows have websites
        websites_count = df[website_column].notna().sum()
        console.print(f"[blue]Found {websites_count} rows with website data[/blue]")

        if websites_count == 0:
            console.print("[red]No websites found in the selected column[/red]")
            return None

        # Step 2: Set processing limit
        amount = None
        if websites_count > 50:
            limit_choice = questionary.select(
                f"Process all {websites_count} websites or limit for testing?",
                choices=[
                    questionary.Choice(f"🚀 Process all {websites_count} websites", None),
                    questionary.Choice("🧪 Limit for testing", "limit")
                ]
            ).ask()

            if limit_choice == "limit":
                amount = questionary.text(
                    "Enter number of websites to process:",
                    default="20",
                    validate=lambda x: x.isdigit() and int(x) > 0 or "Please enter a positive number"
                ).ask()
                amount = int(amount) if amount else 20

        # Step 3: Performance settings
        performance_choice = questionary.select(
            "Select performance profile:",
            choices=[
                questionary.Choice("🐌 Conservative (5 batch, 2 concurrent)", "conservative"),
                questionary.Choice("🏃 Balanced (25 batch, 5 concurrent)", "balanced"),
                questionary.Choice("🚀 Aggressive (50 batch, 10 concurrent)", "aggressive"),
                questionary.Choice("⚙️ Custom settings", "custom")
            ]
        ).ask()

        if performance_choice == "conservative":
            batch_size, max_concurrent = 5, 2
        elif performance_choice == "balanced":
            batch_size, max_concurrent = 25, 5
        elif performance_choice == "aggressive":
            batch_size, max_concurrent = 50, 10
        else:  # custom
            batch_size = int(questionary.text(
                "Batch size:",
                default="25",
                validate=lambda x: x.isdigit() and int(x) > 0 or "Please enter a positive number"
            ).ask() or "25")

            max_concurrent = int(questionary.text(
                "Max concurrent requests:",
                default="5",
                validate=lambda x: x.isdigit() and int(x) > 0 or "Please enter a positive number"
            ).ask() or "5")

        # Step 4: Output file
        custom_output = questionary.confirm("Specify custom output filename?").ask()
        output_file = None

        if custom_output:
            output_file = questionary.text(
                "Enter output filename (without extension):",
                validate=lambda x: len(x.strip()) > 0 or "Filename cannot be empty"
            ).ask()

            if output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"{output_file}_{timestamp}.csv"

        return {
            'website_column': website_column,
            'amount': amount,
            'batch_size': batch_size,
            'max_concurrent': max_concurrent,
            'output_file': output_file
        }

    except Exception as e:
        console.print(f"[red]Error reading CSV file: {e}[/red]")
        return None


def _confirm_scraping_config(csv_file: str, config: dict, scrape_type: str) -> bool:
    """Show configuration summary and get confirmation"""
    from rich.table import Table

    table = Table(title=f"{scrape_type.title()} Scraping Configuration", show_header=True, header_style="bold blue")
    table.add_column("Setting", style="cyan", no_wrap=True)
    table.add_column("Value", style="white")

    table.add_row("Input File", csv_file)
    table.add_row("Website Column", config['website_column'])
    table.add_row("Processing Limit", str(config['amount']) if config['amount'] else "All rows")
    table.add_row("Batch Size", str(config['batch_size']))
    table.add_row("Max Concurrent", str(config['max_concurrent']))
    table.add_row("Output File", config['output_file'] or "Auto-generated")

    console.print(table)

    return questionary.confirm("Start scraping with these settings?").ask() or False


def _configure_scraping_settings():
    """Configure default scraping settings"""
    console.print(Panel(
        "⚙️ Scraping Settings\n\n"
        "Configure default settings for email and contact scraping.",
        title="Scraping Configuration",
        border_style="yellow"
    ))

    console.print("[blue]Current settings will be used as defaults for future scraping operations.[/blue]")

    # This could integrate with the config system
    console.print("[yellow]Settings configuration coming soon![/yellow]")
    console.print("[dim]For now, settings are configured per scraping session.[/dim]")


@scrape_app.callback()
def scrape_callback():
    """Email and contact scraping commands"""
    pass
