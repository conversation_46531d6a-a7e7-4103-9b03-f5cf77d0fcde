"""
Category management commands for the CLI
"""
import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.columns import Columns
from rich.text import Text
from typing import Optional
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cli.categories import CategoryManager, CategoryGroup, CATEGORY_GROUPS

console = Console()

# Create categories app
categories_app = typer.Typer(help="Manage and view business categories")


@categories_app.command("list")
def list_categories(
    group: Optional[str] = typer.Option(
        None, "--group", "-g", help="Filter by category group"
    ),
    detailed: bool = typer.Option(
        False, "--detailed", "-d", help="Show detailed category information"
    )
):
    """
    📋 List all available business categories
    """
    manager = CategoryManager()

    if group:
        # Try to find the group by name
        target_group = None
        for cat_group in CategoryGroup:
            if cat_group.value.lower() == group.lower():
                target_group = cat_group
                break

        if not target_group:
            console.print(f"[red]Error: Unknown category group '{group}'[/red]")
            console.print("[yellow]Available groups:[/yellow]")
            for cat_group in CategoryGroup:
                console.print(f"  • {cat_group.value}")
            raise typer.Exit(1)

        categories = manager.get_categories_by_group(target_group)
        title = f"Categories in {target_group.value}"
    else:
        categories = list(manager.get_all_categories().values())
        title = "All Business Categories"

    if detailed:
        # Show detailed table
        table = Table(title=title, show_header=True, header_style="bold blue")
        table.add_column("Name", style="cyan", no_wrap=True)
        table.add_column("Description", style="white")
        table.add_column("Keywords", style="dim")

        for category in categories:
            keywords = ", ".join(category.keywords[:3])  # Show first 3 keywords
            if len(category.keywords) > 3:
                keywords += "..."

            table.add_row(
                category.name,
                category.description,
                keywords
            )

        console.print(table)
    else:
        # Show grouped layout
        console.print(Panel(title, border_style="blue"))

        if group:
            # Show categories in columns for single group
            category_panels = []
            for category in categories:
                panel = Panel(
                    f"[bold]{category.name}[/bold]\n{category.description}",
                    border_style="cyan",
                    padding=(0, 1)
                )
                category_panels.append(panel)

            console.print(Columns(category_panels, equal=True, expand=True))
        else:
            # Show by groups
            for cat_group in CategoryGroup:
                group_categories = manager.get_categories_by_group(cat_group)
                if group_categories:
                    console.print(f"\n[bold yellow]{cat_group.value}[/bold yellow]")
                    for category in group_categories:
                        console.print(f"  • [cyan]{category.name}[/cyan] - {category.description}")


@categories_app.command("search")
def search_categories(
    query: str = typer.Argument(..., help="Search term for categories")
):
    """
    🔍 Search for specific business categories
    """
    manager = CategoryManager()
    results = manager.search_categories(query)

    if not results:
        console.print(f"[yellow]No categories found matching '[bold]{query}[/bold]'[/yellow]")
        console.print("\n[dim]Try searching for terms like: restaurant, retail, medical, auto, etc.[/dim]")
        return

    console.print(f"[green]Found {len(results)} categories matching '[bold]{query}[/bold]':[/green]\n")

    table = Table(show_header=True, header_style="bold green")
    table.add_column("Name", style="cyan", no_wrap=True)
    table.add_column("Description", style="white")
    table.add_column("Matching Keywords", style="dim")

    for category in results:
        # Find which keywords matched
        matching_keywords = [kw for kw in category.keywords if query.lower() in kw.lower()]
        if not matching_keywords:
            # Check if query matches name or description
            if query.lower() in category.name.lower():
                matching_keywords = ["name"]
            elif query.lower() in category.description.lower():
                matching_keywords = ["description"]

        table.add_row(
            category.name,
            category.description,
            ", ".join(matching_keywords[:3])
        )

    console.print(table)


@categories_app.command("groups")
def list_groups():
    """
    📂 List all category groups
    """
    console.print(Panel(
        "Business Category Groups",
        title="Groups",
        border_style="magenta"
    ))

    manager = CategoryManager()

    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Group", style="magenta", no_wrap=True)
    table.add_column("Categories", style="white")
    table.add_column("Count", style="cyan", justify="center")

    for group in CategoryGroup:
        categories = manager.get_categories_by_group(group)
        category_names = [cat.name for cat in categories]

        table.add_row(
            group.value,
            ", ".join(category_names[:3]) + ("..." if len(category_names) > 3 else ""),
            str(len(category_names))
        )

    console.print(table)
    console.print("\n[dim]Use 'categories list --group <group_name>' to see categories in a specific group[/dim]")


@categories_app.command("info")
def category_info(
    category: str = typer.Argument(..., help="Category name or ID to get detailed information about")
):
    """
    ℹ️ Get detailed information about a specific category
    """
    manager = CategoryManager()

    # Try to find category by ID first, then by name
    cat_mapping = manager.get_category(category.lower().replace(" ", "_"))

    if not cat_mapping:
        # Search by name
        all_categories = manager.get_all_categories()
        for cat_id, cat_data in all_categories.items():
            if cat_data.name.lower() == category.lower():
                cat_mapping = cat_data
                break

    if not cat_mapping:
        console.print(f"[red]Error: Category '{category}' not found[/red]")
        console.print("[yellow]Use 'categories search <term>' to find categories[/yellow]")
        raise typer.Exit(1)

    # Create detailed info panel
    info_text = f"""[bold cyan]{cat_mapping.name}[/bold cyan]

[bold]Description:[/bold]
{cat_mapping.description}

[bold]Keywords:[/bold]
{', '.join(cat_mapping.keywords)}

[bold]API Mappings:[/bold]
• Google Places: {', '.join(cat_mapping.google_places_types) if cat_mapping.google_places_types else 'None'}
• Overpass Amenities: {', '.join(cat_mapping.overpass_amenities) if cat_mapping.overpass_amenities else 'None'}
• Overpass Shops: {', '.join(cat_mapping.overpass_shops) if cat_mapping.overpass_shops else 'None'}
• Yellow Pages: {', '.join(cat_mapping.yellow_pages_terms) if cat_mapping.yellow_pages_terms else 'None'}"""

    console.print(Panel(
        info_text,
        title="Category Information",
        border_style="cyan",
        padding=(1, 2)
    ))


@categories_app.callback()
def categories_callback():
    """Business category management commands"""
    pass
