"""
Data collection commands for the CLI
"""
import typer
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.table import Table
from rich.text import Text
from typing import Optional, List
import questionary
import sys
import os
from pathlib import Path
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cli.categories import CategoryManager, CategoryGroup, CATEGORY_GROUPS
from cli.location import LocationService
from cli.collection_service import CollectionService
from cli.menu_helpers import enhanced_category_selection, enhanced_location_selection

console = Console()

# Create collect app
collect_app = typer.Typer(help="Collect business data from various sources")


@collect_app.command("interactive")
def collect_interactive():
    """
    🎯 Interactive business data collection

    Guided process to collect business data with step-by-step prompts.
    """
    console.print(Panel(
        "Welcome to Interactive Business Data Collection! 🎯\n\n"
        "This wizard will guide you through collecting business data from multiple sources.",
        title="Interactive Collection",
        border_style="green"
    ))

    try:
        # Step 1: Category Selection
        console.print("\n[bold blue]Step 1: Select Business Categories[/bold blue]")
        selected_categories = enhanced_category_selection()

        if not selected_categories:
            console.print("[yellow]No categories selected. Exiting...[/yellow]")
            return

        # Step 2: Location Input
        console.print("\n[bold blue]Step 2: Set Search Location[/bold blue]")
        location = enhanced_location_selection()

        if not location:
            console.print("[yellow]No location provided. Exiting...[/yellow]")
            return

        # Step 3: Radius Selection
        console.print("\n[bold blue]Step 3: Set Search Radius[/bold blue]")
        radius = _enhanced_radius_selection()

        # Step 4: Data Source Selection
        console.print("\n[bold blue]Step 4: Select Data Sources[/bold blue]")
        sources = _enhanced_source_selection()

        # Step 5: Output Configuration
        console.print("\n[bold blue]Step 5: Configure Output[/bold blue]")
        output_config = _enhanced_output_configuration()

        # Step 6: Summary and Confirmation
        console.print("\n[bold blue]Step 6: Review and Confirm[/bold blue]")
        if _show_collection_summary(selected_categories, location, radius, sources, output_config):
            console.print("\n[green]Starting data collection...[/green]")

            # Execute collection
            collection_service = CollectionService()
            result = collection_service.collect_businesses(
                category_ids=selected_categories,
                location_data=location,
                radius_miles=radius,
                sources=sources,
                output_config=output_config
            )

            console.print(f"\n[bold green]Collection completed successfully![/bold green]")
            console.print(f"Collected {result['stats']['total_after_processing']} businesses")
        else:
            console.print("[yellow]Collection cancelled.[/yellow]")

    except KeyboardInterrupt:
        console.print("\n[yellow]Collection cancelled by user.[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error during interactive collection: {str(e)}[/red]")


def _interactive_category_selection() -> List[str]:
    """Interactive category selection with grouped display"""
    manager = CategoryManager()

    # First, let user choose selection method
    selection_method = questionary.select(
        "How would you like to select categories?",
        choices=[
            "Browse by category groups",
            "Search for specific categories",
            "Select from all categories"
        ]
    ).ask()

    if not selection_method:
        return []

    selected_categories = []

    if selection_method == "Browse by category groups":
        # Show category groups
        group_choices = [group.value for group in CategoryGroup]

        selected_groups = questionary.checkbox(
            "Select category groups to explore:",
            choices=group_choices
        ).ask()

        if not selected_groups:
            return []

        # For each selected group, show categories
        for group_name in selected_groups:
            group = next(g for g in CategoryGroup if g.value == group_name)
            categories = manager.get_categories_by_group(group)

            category_choices = [
                questionary.Choice(
                    title=f"{cat.name} - {cat.description}",
                    value=cat_id
                )
                for cat_id, cat in manager.get_all_categories().items()
                if cat in categories
            ]

            group_selections = questionary.checkbox(
                f"Select categories from {group_name}:",
                choices=category_choices
            ).ask()

            if group_selections:
                selected_categories.extend(group_selections)

    elif selection_method == "Search for specific categories":
        while True:
            search_query = questionary.text(
                "Enter search term (or press Enter to finish):"
            ).ask()

            if not search_query:
                break

            results = manager.search_categories(search_query)
            if not results:
                console.print(f"[yellow]No categories found for '{search_query}'[/yellow]")
                continue

            result_choices = [
                questionary.Choice(
                    title=f"{cat.name} - {cat.description}",
                    value=next(cat_id for cat_id, cat_data in manager.get_all_categories().items() if cat_data == cat)
                )
                for cat in results
            ]

            search_selections = questionary.checkbox(
                f"Select categories from search results:",
                choices=result_choices
            ).ask()

            if search_selections:
                selected_categories.extend(search_selections)

    else:  # Select from all categories
        all_categories = manager.get_all_categories()
        category_choices = [
            questionary.Choice(
                title=f"{cat.name} - {cat.description}",
                value=cat_id
            )
            for cat_id, cat in all_categories.items()
        ]

        selected_categories = questionary.checkbox(
            "Select categories:",
            choices=category_choices
        ).ask()

    # Remove duplicates
    selected_categories = list(set(selected_categories or []))

    if selected_categories:
        console.print(f"[green]Selected {len(selected_categories)} categories:[/green]")
        for cat_id in selected_categories:
            cat = manager.get_category(cat_id)
            if cat:
                console.print(f"  • {cat.name}")

    return selected_categories


def _interactive_location_input() -> Optional[dict]:
    """Interactive location input with validation"""
    location_service = LocationService()

    while True:
        location_input = questionary.text(
            "Enter location (city name, 'City, State', or coordinates 'lat,lng'):",
            validate=lambda x: len(x.strip()) > 0 or "Location cannot be empty"
        ).ask()

        if not location_input:
            return None

        # Parse the input
        parsed = location_service.parse_location_input(location_input)

        if parsed['type'] == 'invalid':
            console.print(f"[red]Error: {parsed['error']}[/red]")
            continue

        if parsed['type'] == 'coordinates':
            # Direct coordinates
            console.print(f"[green]Using coordinates: {parsed['latitude']}, {parsed['longitude']}[/green]")
            return {
                'type': 'coordinates',
                'latitude': parsed['latitude'],
                'longitude': parsed['longitude'],
                'name': f"{parsed['latitude']}, {parsed['longitude']}"
            }

        else:  # location_name
            # Try to geocode
            console.print(f"[yellow]Looking up location: {parsed['query']}...[/yellow]")

            location = location_service.geocode_location(parsed['query'])

            if not location:
                console.print(f"[red]Could not find location: {parsed['query']}[/red]")
                retry = questionary.confirm("Try a different location?").ask()
                if not retry:
                    return None
                continue

            # Show found location and confirm
            console.print(f"[green]Found location:[/green]")
            console.print(f"  Address: {location.formatted_address}")
            console.print(f"  Coordinates: {location.latitude}, {location.longitude}")

            confirm = questionary.confirm("Use this location?").ask()
            if confirm:
                return {
                    'type': 'geocoded',
                    'latitude': location.latitude,
                    'longitude': location.longitude,
                    'name': location.formatted_address,
                    'city': location.city,
                    'state': location.state,
                    'country': location.country
                }
            else:
                continue


def _interactive_radius_selection() -> float:
    """Interactive radius selection"""
    radius_options = [
        "1 mile",
        "5 miles",
        "10 miles",
        "15 miles",
        "25 miles",
        "50 miles",
        "Custom radius"
    ]

    radius_choice = questionary.select(
        "Select search radius:",
        choices=radius_options
    ).ask()

    if not radius_choice:
        return 10.0  # Default

    if radius_choice == "Custom radius":
        while True:
            try:
                custom_radius = questionary.text(
                    "Enter radius in miles:",
                    validate=lambda x: x.replace('.', '').isdigit() or "Please enter a valid number"
                ).ask()

                if not custom_radius:
                    return 10.0

                radius = float(custom_radius)
                if radius <= 0:
                    console.print("[red]Radius must be greater than 0[/red]")
                    continue
                if radius > 100:
                    console.print("[yellow]Warning: Large radius may result in many results and longer processing time[/yellow]")

                return radius
            except ValueError:
                console.print("[red]Please enter a valid number[/red]")
    else:
        # Extract number from choice
        return float(radius_choice.split()[0])


def _interactive_source_selection() -> List[str]:
    """Interactive data source selection"""
    source_options = [
        questionary.Choice("Google Places API (most reliable, requires API key)", "google"),
        questionary.Choice("Overpass API (free OpenStreetMap data)", "overpass"),
        questionary.Choice("Yellow Pages (web scraping)", "yellowpages")
    ]

    selected_sources = questionary.checkbox(
        "Select data sources to use:",
        choices=source_options
    ).ask()

    if not selected_sources:
        # Default to all sources
        return ["google", "overpass", "yellowpages"]

    return selected_sources


def _interactive_output_configuration() -> dict:
    """Interactive output configuration"""
    output_format = questionary.select(
        "Select output format:",
        choices=["CSV", "JSON", "Both CSV and JSON"]
    ).ask()

    custom_filename = questionary.confirm(
        "Use custom filename?"
    ).ask()

    filename = None
    if custom_filename:
        filename = questionary.text(
            "Enter filename (without extension):",
            validate=lambda x: len(x.strip()) > 0 or "Filename cannot be empty"
        ).ask()

    return {
        'format': output_format or "CSV",
        'custom_filename': filename
    }


def _show_collection_summary(categories: List[str], location: dict, radius: float,
                           sources: List[str], output_config: dict) -> bool:
    """Show collection summary and get confirmation"""
    manager = CategoryManager()

    # Create summary table
    table = Table(title="Collection Summary", show_header=True, header_style="bold blue")
    table.add_column("Setting", style="cyan", no_wrap=True)
    table.add_column("Value", style="white")

    # Categories
    category_names = []
    for cat_id in categories:
        cat = manager.get_category(cat_id)
        if cat:
            category_names.append(cat.name)

    table.add_row("Categories", ", ".join(category_names))
    table.add_row("Location", location['name'])
    table.add_row("Coordinates", f"{location['latitude']}, {location['longitude']}")
    table.add_row("Radius", f"{radius} miles")
    table.add_row("Data Sources", ", ".join(sources))
    table.add_row("Output Format", output_config['format'])

    if output_config['custom_filename']:
        table.add_row("Filename", output_config['custom_filename'])

    console.print(table)

    return questionary.confirm(
        "\nProceed with data collection?"
    ).ask() or False


@collect_app.command("quick")
def collect_quick(
    categories: Optional[List[str]] = typer.Option(
        None, "--category", "-c", help="Business categories to search for"
    ),
    location: Optional[str] = typer.Option(
        None, "--location", "-l", help="City or location to search in"
    ),
    radius: Optional[float] = typer.Option(
        10.0, "--radius", "-r", help="Search radius in miles"
    ),
    sources: Optional[List[str]] = typer.Option(
        ["google", "overpass", "yellowpages"],
        "--source", "-s",
        help="Data sources to use (google, overpass, yellowpages)"
    ),
    output: Optional[str] = typer.Option(
        None, "--output", "-o", help="Output file path"
    ),
    amount: Optional[int] = typer.Option(
        None, "--amount", "-a", help="Limit number of results per category (for testing)"
    )
):
    """
    ⚡ Quick business data collection with command-line arguments
    """
    if not categories:
        console.print("[red]Error: At least one category is required[/red]")
        console.print("[yellow]Use 'categories list' to see available categories[/yellow]")
        raise typer.Exit(1)

    if not location:
        console.print("[red]Error: Location is required[/red]")
        raise typer.Exit(1)

    # Validate categories
    manager = CategoryManager()
    valid_categories = []
    invalid_categories = []

    for category in categories:
        # Try exact match first
        cat = manager.get_category(category.lower().replace(" ", "_"))
        if cat:
            valid_categories.append(category.lower().replace(" ", "_"))
        else:
            # Try search
            search_results = manager.search_categories(category)
            if search_results:
                # Use first match
                cat_id = next(cat_id for cat_id, cat_data in manager.get_all_categories().items()
                             if cat_data == search_results[0])
                valid_categories.append(cat_id)
                console.print(f"[yellow]Matched '{category}' to '{search_results[0].name}'[/yellow]")
            else:
                invalid_categories.append(category)

    if invalid_categories:
        console.print(f"[red]Invalid categories: {', '.join(invalid_categories)}[/red]")
        console.print("[yellow]Use 'categories search <term>' to find valid categories[/yellow]")
        raise typer.Exit(1)

    # Validate location
    location_service = LocationService()
    parsed_location = location_service.parse_location_input(location)

    if parsed_location['type'] == 'invalid':
        console.print(f"[red]Invalid location: {parsed_location['error']}[/red]")
        raise typer.Exit(1)

    location_data = None
    if parsed_location['type'] == 'coordinates':
        location_data = {
            'type': 'coordinates',
            'latitude': parsed_location['latitude'],
            'longitude': parsed_location['longitude'],
            'name': f"{parsed_location['latitude']}, {parsed_location['longitude']}"
        }
    else:
        console.print(f"[yellow]Looking up location: {location}...[/yellow]")
        geocoded = location_service.geocode_location(location)
        if not geocoded:
            console.print(f"[red]Could not find location: {location}[/red]")
            raise typer.Exit(1)

        location_data = {
            'type': 'geocoded',
            'latitude': geocoded.latitude,
            'longitude': geocoded.longitude,
            'name': geocoded.formatted_address
        }

    # Validate sources
    valid_sources = ["google", "overpass", "yellowpages"]
    invalid_sources = [s for s in sources if s not in valid_sources]
    if invalid_sources:
        console.print(f"[red]Invalid sources: {', '.join(invalid_sources)}[/red]")
        console.print(f"[yellow]Valid sources: {', '.join(valid_sources)}[/yellow]")
        raise typer.Exit(1)

    # Show summary
    console.print(Panel(
        f"[bold]Quick Collection Summary[/bold]\n\n"
        f"Categories: {', '.join([manager.get_category(c).name for c in valid_categories])}\n"
        f"Location: {location_data['name']}\n"
        f"Coordinates: {location_data['latitude']}, {location_data['longitude']}\n"
        f"Radius: {radius} miles\n"
        f"Sources: {', '.join(sources)}\n"
        f"Output: {output or 'Default filename'}",
        title="Collection Configuration",
        border_style="green"
    ))

    console.print("\n[green]Starting data collection...[/green]")

    # Execute collection
    collection_service = CollectionService()
    result = collection_service.collect_businesses(
        category_ids=valid_categories,
        location_data=location_data,
        radius_miles=radius,
        sources=sources,
        output_config={'format': 'CSV', 'custom_filename': output},
        max_results_per_category=amount
    )

    console.print(f"\n[bold green]Quick collection completed successfully![/bold green]")
    console.print(f"Collected {result['stats']['total_after_processing']} businesses")


@collect_app.command("facebook-emails")
def extract_facebook_emails(
    csv_file: str = typer.Argument(..., help="CSV file with business data"),
    token: Optional[str] = typer.Option(None, "--token", "-t", help="Apify API token"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file path"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging")
):
    """
    📘 Extract emails from Facebook business pages using Apify

    Processes businesses that have Facebook pages but no emails.
    """
    import logging

    if verbose:
        logging.basicConfig(level=logging.DEBUG)

    console.print(Panel(
        "📘 Facebook Email Extraction\n\n"
        "Extract emails from Facebook business pages for businesses without contact information.",
        title="Facebook Email Extraction",
        border_style="blue"
    ))

    # Check if file exists
    if not os.path.exists(csv_file):
        console.print(f"[red]Error: File not found: {csv_file}[/red]")
        raise typer.Exit(1)

    # Get Apify token
    apify_token = token or os.getenv('APIFY_TOKEN')
    if not apify_token:
        console.print("[red]Error: Apify token is required.[/red]")
        console.print("Use --token option or set APIFY_TOKEN environment variable.")
        console.print("Get your token from: https://console.apify.com/account/integrations")
        raise typer.Exit(1)

    try:
        from collectors.apify_facebook import extract_facebook_emails_from_csv_with_social_platform

        # Generate output filename if not provided
        if not output:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = Path(csv_file).stem
            output = f"{base_name}_facebook_emails_{timestamp}.csv"

        console.print(f"[green]Processing: {csv_file}[/green]")
        console.print(f"[green]Output: {output}[/green]")

        # Run extraction
        enhanced_df = extract_facebook_emails_from_csv_with_social_platform(
            csv_file_path=csv_file,
            apify_token=apify_token,
            output_file=output
        )

        # Show results
        facebook_emails = len(enhanced_df[enhanced_df['facebook_email'].notna() & (enhanced_df['facebook_email'] != '')])
        total_businesses = len(enhanced_df)

        console.print(f"\n[bold green]✅ Extraction completed![/bold green]")
        console.print(f"📧 Facebook emails extracted: {facebook_emails}")
        console.print(f"📊 Total businesses: {total_businesses}")
        console.print(f"💾 Results saved to: {output}")

    except ImportError:
        console.print("[red]Error: Facebook extraction dependencies not available.[/red]")
        console.print("Run: pip install apify-client")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error during extraction: {str(e)}[/red]")
        raise typer.Exit(1)


@collect_app.command("pipeline")
def full_pipeline():
    """
    🚀 Full Pipeline - Data Collection + Email Extraction + Facebook Enhancement

    Complete workflow from business data collection to email/contact scraping with Facebook enhancement.
    """
    console.print(Panel(
        "🚀 Full Business Data Pipeline\n\n"
        "This comprehensive workflow will:\n"
        "1. Collect business data from multiple sources\n"
        "2. Process and deduplicate the results\n"
        "3. Extract emails and contacts from business websites\n"
        "4. Extract additional emails from Facebook business pages\n"
        "5. Provide final results with comprehensive contact information",
        title="Complete Pipeline",
        border_style="magenta"
    ))

    try:
        # Step 1: Data Collection
        console.print("\n[bold magenta]🎯 Phase 1: Business Data Collection[/bold magenta]")

        # Use enhanced category selection
        console.print("\n[bold blue]Step 1: Select Business Categories[/bold blue]")
        selected_categories = enhanced_category_selection()

        if not selected_categories:
            console.print("[yellow]No categories selected. Exiting pipeline.[/yellow]")
            return

        # Use enhanced location selection
        console.print("\n[bold blue]Step 2: Set Search Location[/bold blue]")
        location = enhanced_location_selection()

        if not location:
            console.print("[yellow]No location provided. Exiting pipeline.[/yellow]")
            return

        # Enhanced radius selection
        console.print("\n[bold blue]Step 3: Set Search Radius[/bold blue]")
        radius = _enhanced_radius_selection()

        # Enhanced source selection
        console.print("\n[bold blue]Step 4: Select Data Sources[/bold blue]")
        sources = _enhanced_source_selection()

        # Pipeline-specific output configuration
        console.print("\n[bold blue]Step 5: Configure Pipeline Output[/bold blue]")
        pipeline_config = _pipeline_output_configuration()

        # Show collection summary and confirm
        console.print("\n[bold blue]Step 6: Review Collection Settings[/bold blue]")
        if not _show_collection_summary(selected_categories, location, radius, sources, pipeline_config):
            console.print("[yellow]Pipeline cancelled.[/yellow]")
            return

        console.print("\n[green]Starting business data collection...[/green]")

        # Execute collection
        collection_service = CollectionService()
        collection_result = collection_service.collect_businesses(
            category_ids=selected_categories,
            location_data=location,
            radius_miles=radius,
            sources=sources,
            output_config=pipeline_config,
        )

        businesses_collected = collection_result['stats']['total_after_processing']

        if businesses_collected == 0:
            console.print("[red]No businesses collected. Pipeline cannot continue.[/red]")
            return

        console.print(f"\n[bold green]✅ Collection Phase Complete![/bold green]")
        console.print(f"Collected {businesses_collected} businesses")

        # Get the output file path
        collection_file = None
        if 'export_paths' in collection_result['stats'] and 'csv' in collection_result['stats']['export_paths']:
            collection_file = collection_result['stats']['export_paths']['csv']
        else:
            console.print("[red]Error: Could not find collection output file[/red]")
            return

        # Step 2: Email/Contact Extraction
        console.print(f"\n[bold magenta]📧 Phase 2: Email & Contact Extraction[/bold magenta]")

        if not _should_continue_to_scraping(businesses_collected):
            console.print("[yellow]Skipping scraping phase. Pipeline complete with business data only.[/yellow]")
            return

        # Configure scraping for pipeline
        scraping_config = _pipeline_scraping_configuration(collection_file, businesses_collected)

        if not scraping_config:
            console.print("[yellow]Scraping configuration cancelled. Pipeline complete with business data only.[/yellow]")
            return

        # Import scraping functionality
        try:
            from cli.commands.scrape import _scrape_contacts_async, SCRAPING_AVAILABLE

            if not SCRAPING_AVAILABLE:
                console.print("[red]Scraping functionality not available. Pipeline complete with business data only.[/red]")
                return

            console.print("\n[green]Starting email and contact extraction...[/green]")

            # Run scraping
            import asyncio
            asyncio.run(_scrape_contacts_async(
                collection_file,
                scraping_config['output_file'],
                scraping_config['batch_size'],
                scraping_config['max_concurrent'],
                scraping_config['website_column'],
                scraping_config['amount']
            ))

            # Check for Facebook email extraction opportunity
            facebook_enhanced_file = _check_and_run_facebook_extraction(scraping_config['output_file'])

            console.print(f"\n[bold green]🎉 Full Pipeline Complete![/bold green]")
            console.print(f"✅ Business data: {collection_file}")
            console.print(f"✅ Contact data: {scraping_config['output_file']}")
            if facebook_enhanced_file:
                console.print(f"✅ Facebook enhanced: {facebook_enhanced_file}")

        except ImportError:
            console.print("[red]Scraping functionality not available. Pipeline complete with business data only.[/red]")

    except KeyboardInterrupt:
        console.print("\n[yellow]Pipeline cancelled by user.[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error during pipeline execution: {str(e)}[/red]")


def _enhanced_radius_selection() -> float:
    """Enhanced radius selection with visual indicators"""
    radius_options = [
        questionary.Choice("🏠 1 mile (very local)", 1.0),
        questionary.Choice("🏘️ 5 miles (neighborhood)", 5.0),
        questionary.Choice("🏙️ 10 miles (city area)", 10.0),
        questionary.Choice("🌆 15 miles (metro area)", 15.0),
        questionary.Choice("🗺️ 25 miles (regional)", 25.0),
        questionary.Choice("🌍 50 miles (wide area)", 50.0),
        questionary.Choice("⚙️ Custom radius", "custom")
    ]

    radius_choice = questionary.select(
        "Select search radius:",
        choices=radius_options,
        style=questionary.Style([
            ('question', 'bold'),
            ('answer', 'fg:#ff9d00 bold'),
            ('pointer', 'fg:#ff9d00 bold'),
            ('highlighted', 'fg:#ff9d00 bold'),
        ])
    ).ask()

    if radius_choice == "custom":
        while True:
            try:
                custom_radius = questionary.text(
                    "Enter radius in miles:",
                    validate=lambda x: x.replace('.', '').isdigit() or "Please enter a valid number"
                ).ask()

                if not custom_radius:
                    return 10.0

                radius = float(custom_radius)
                if radius <= 0:
                    console.print("[red]Radius must be greater than 0[/red]")
                    continue
                if radius > 100:
                    console.print("[yellow]Warning: Large radius may result in many results and longer processing time[/yellow]")

                return radius
            except ValueError:
                console.print("[red]Please enter a valid number[/red]")
    else:
        return radius_choice or 10.0


def _enhanced_source_selection() -> List[str]:
    """Enhanced data source selection with descriptions"""
    source_options = [
        questionary.Choice("🏢 Google Places API (most reliable, requires API key)", "google"),
        questionary.Choice("🗺️ Overpass API (free OpenStreetMap data)", "overpass"),
        questionary.Choice("📞 Yellow Pages (web scraping with AI)", "yellowpages")
    ]

    console.print(Panel(
        "📊 Data Source Selection\n\n"
        "Select which data sources to use for collection.\n"
        "Multiple sources provide better coverage but take longer.",
        title="Data Sources",
        border_style="blue"
    ))

    selected_sources = questionary.checkbox(
        "Select data sources to use (select multiple for better coverage):",
        choices=source_options,
        style=questionary.Style([
            ('question', 'bold'),
            ('answer', 'fg:#ff9d00 bold'),
            ('pointer', 'fg:#ff9d00 bold'),
            ('highlighted', 'fg:#ff9d00 bold'),
            ('selected', 'fg:#cc5454'),
        ])
    ).ask()

    if not selected_sources:
        console.print("[yellow]No sources selected. Using all sources by default.[/yellow]")
        return ["google", "overpass", "yellowpages"]

    return selected_sources


def _enhanced_output_configuration() -> dict:
    """Enhanced output configuration with preview"""
    console.print(Panel(
        "📁 Output Configuration\n\n"
        "Configure how your results will be saved.",
        title="Output Settings",
        border_style="green"
    ))

    # Output format selection
    format_choice = questionary.select(
        "Select output format:",
        choices=[
            questionary.Choice("📊 CSV (spreadsheet format)", "CSV"),
            questionary.Choice("📋 JSON (structured data)", "JSON"),
            questionary.Choice("📈 Both CSV and JSON", "Both CSV and JSON")
        ]
    ).ask()

    # Custom filename option
    custom_filename = questionary.confirm(
        "Would you like to specify a custom filename?"
    ).ask()

    filename = None
    if custom_filename:
        filename = questionary.text(
            "Enter filename (without extension):",
            validate=lambda x: len(x.strip()) > 0 or "Filename cannot be empty"
        ).ask()

    return {
        'format': format_choice or "CSV",
        'custom_filename': filename
    }


def _pipeline_output_configuration() -> dict:
    """Pipeline-specific output configuration"""
    console.print(Panel(
        "📁 Pipeline Output Configuration\n\n"
        "Configure output for the complete pipeline workflow.",
        title="Pipeline Output",
        border_style="green"
    ))

    # Always use CSV for pipeline (needed for scraping)
    console.print("[blue]Pipeline will generate CSV output (required for email extraction phase)[/blue]")

    # Custom filename option
    custom_filename = questionary.confirm(
        "Would you like to specify a custom base filename?"
    ).ask()

    filename = None
    if custom_filename:
        filename = questionary.text(
            "Enter base filename (extensions will be added automatically):",
            validate=lambda x: len(x.strip()) > 0 or "Filename cannot be empty"
        ).ask()

    return {
        'format': 'CSV',
        'custom_filename': filename
    }


def _should_continue_to_scraping(businesses_count: int) -> bool:
    """Ask user if they want to continue to scraping phase"""
    console.print(Panel(
        f"📊 Collection Summary\n\n"
        f"Successfully collected {businesses_count} businesses.\n\n"
        f"The next phase will extract emails and contact information from business websites.\n"
        f"This process may take some time depending on the number of businesses.",
        title="Continue to Scraping?",
        border_style="yellow"
    ))

    return questionary.confirm(
        "Continue to email and contact extraction phase?"
    ).ask() or False


def _pipeline_scraping_configuration(collection_file: str, businesses_count: int) -> Optional[dict]:
    """Configure scraping for pipeline workflow"""
    try:
        import pandas as pd

        # Load the collection file to check for website data
        df = pd.read_csv(collection_file)

        # Find website column
        website_columns = [col for col in df.columns if 'website' in col.lower() or 'url' in col.lower()]

        if not website_columns:
            console.print("[red]No website columns found in collected data.[/red]")
            console.print("[yellow]Scraping phase cannot continue without website URLs.[/yellow]")
            return None

        website_column = website_columns[0]  # Use first website column found
        websites_count = df[website_column].notna().sum()

        console.print(f"[green]Found {websites_count} businesses with website data[/green]")

        if websites_count == 0:
            console.print("[red]No businesses have website URLs for scraping.[/red]")
            return None

        console.print(Panel(
            "⚙️ Scraping Configuration\n\n"
            "Configure the email and contact extraction settings.",
            title="Scraping Setup",
            border_style="cyan"
        ))

        # Processing limit
        amount = None
        if websites_count > 100:
            limit_choice = questionary.select(
                f"Process all {websites_count} websites or limit for faster processing?",
                choices=[
                    questionary.Choice(f"🚀 Process all {websites_count} websites", None),
                    questionary.Choice("⚡ Limit for faster processing", "limit")
                ]
            ).ask()

            if limit_choice == "limit":
                suggested_limit = min(50, websites_count)
                amount = questionary.text(
                    f"Enter number of websites to process (suggested: {suggested_limit}):",
                    default=str(suggested_limit),
                    validate=lambda x: x.isdigit() and int(x) > 0 or "Please enter a positive number"
                ).ask()
                amount = int(amount) if amount else suggested_limit

        # Performance settings
        performance_choice = questionary.select(
            "Select scraping performance profile:",
            choices=[
                questionary.Choice("🐌 Conservative (slower, safer)", "conservative"),
                questionary.Choice("🏃 Balanced (recommended)", "balanced"),
                questionary.Choice("🚀 Aggressive (faster, higher load)", "aggressive")
            ]
        ).ask()

        if performance_choice == "conservative":
            batch_size, max_concurrent = 10, 3
        elif performance_choice == "aggressive":
            batch_size, max_concurrent = 50, 10
        else:  # balanced
            batch_size, max_concurrent = 25, 5

        # Generate output filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = Path(collection_file).stem
        output_file = f"{base_name}_contacts_{timestamp}.csv"

        return {
            'website_column': website_column,
            'amount': amount,
            'batch_size': batch_size,
            'max_concurrent': max_concurrent,
            'output_file': output_file
        }

    except Exception as e:
        console.print(f"[red]Error configuring scraping: {e}[/red]")
        return None


def _check_and_run_facebook_extraction(contact_file: str) -> Optional[str]:
    """Check for Facebook opportunities and run extraction if beneficial."""
    try:
        import pandas as pd
        import os
        from pathlib import Path

        # Load the contact data
        df = pd.read_csv(contact_file)

        # Check for businesses without emails but with Facebook pages
        # Look for Facebook URLs in social_url column where social_platform is 'facebook'
        facebook_businesses = df[(df['social_platform'] == 'facebook') &
                                (df['social_url'].notna()) &
                                (df['social_url'] != '')]

        # Check for businesses without emails
        no_email_businesses = df[(df['scraped_email'].isna()) | (df['scraped_email'] == '')]

        # Find overlap - businesses with Facebook but no email
        facebook_no_email = facebook_businesses[
            (facebook_businesses['scraped_email'].isna()) |
            (facebook_businesses['scraped_email'] == '')
        ]

        if len(facebook_no_email) == 0:
            console.print("[dim]No Facebook extraction opportunities found (all Facebook businesses already have emails)[/dim]")
            return None

        console.print(f"\n[yellow]📘 Facebook Email Extraction Opportunity[/yellow]")
        console.print(f"Found {len(facebook_no_email)} businesses with Facebook pages but no emails")
        console.print(f"Total Facebook businesses: {len(facebook_businesses)}")
        console.print(f"Total businesses without emails: {len(no_email_businesses)}")

        # Check for Apify token
        apify_token = os.getenv('APIFY_TOKEN')
        if not apify_token:
            # Use the provided token as fallback
            apify_token = "**********************************************"
            console.print(f"\n[green]✅ Using configured Apify token for Facebook extraction[/green]")
        else:
            console.print(f"\n[green]✅ Using environment Apify token for Facebook extraction[/green]")

        # Auto-proceed with Facebook extraction when token is available
        console.print(f"\n[green]🚀 Auto-starting Facebook email extraction for {len(facebook_no_email)} businesses...[/green]")
        console.print(f"[dim]Estimated cost: ~{len(facebook_no_email) * 0.02:.2f} Apify compute units[/dim]")

        console.print(f"\n[green]🚀 Starting Facebook email extraction for {len(facebook_no_email)} businesses...[/green]")
        console.print("[dim]This will enhance your data with additional email addresses from Facebook business pages[/dim]")

        # Import and run Facebook extraction
        from collectors.apify_facebook import extract_facebook_emails_from_csv_with_social_platform

        # Generate output filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = Path(contact_file).stem
        facebook_output_file = f"{base_name}_facebook_enhanced_{timestamp}.csv"

        # Run the extraction
        enhanced_df = extract_facebook_emails_from_csv_with_social_platform(
            csv_file_path=contact_file,
            apify_token=apify_token,
            output_file=facebook_output_file
        )

        # Show results
        facebook_emails_found = len(enhanced_df[enhanced_df['facebook_email'].notna() & (enhanced_df['facebook_email'] != '')])

        if facebook_emails_found > 0:
            console.print(f"\n[bold green]✅ Facebook extraction successful![/bold green]")
            console.print(f"📧 Extracted {facebook_emails_found} additional emails from Facebook pages")
            console.print(f"📈 Facebook success rate: {facebook_emails_found/len(facebook_no_email)*100:.1f}%")
            console.print(f"🎯 Total email coverage improved by {facebook_emails_found} businesses")
            return facebook_output_file
        else:
            console.print(f"\n[yellow]⚠️  No emails found on Facebook pages[/yellow]")
            console.print("[dim]This is normal - not all Facebook business pages have public email addresses[/dim]")
            return facebook_output_file  # Still return the file even if no emails found

    except ImportError:
        console.print("[yellow]Facebook extraction not available (missing dependencies)[/yellow]")
        return None
    except Exception as e:
        console.print(f"[red]Error during Facebook extraction: {str(e)}[/red]")
        return None


def interactive_facebook_extraction():
    """Interactive Facebook email extraction from existing CSV files."""
    console.print(Panel(
        "📘 Facebook Email Extraction\n\n"
        "Extract emails from Facebook business pages for businesses without contact information.",
        title="Facebook Email Extraction",
        border_style="blue"
    ))

    # Ask for CSV file
    csv_file = questionary.path(
        "Select CSV file with business data:",
        validate=lambda x: "File not found" if not os.path.exists(x) else True
    ).ask()

    if not csv_file:
        return

    # Check for Apify token
    apify_token = os.getenv('APIFY_TOKEN')
    if not apify_token:
        console.print("\n[yellow]⚠️  Apify token not found![/yellow]")
        apify_token = questionary.password(
            "Enter your Apify API token (get it from https://console.apify.com/account/integrations):"
        ).ask()

        if not apify_token:
            console.print("[red]Token is required for Facebook extraction.[/red]")
            return

    # Ask for output file
    default_output = f"{Path(csv_file).stem}_facebook_emails_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    output_file = questionary.text(
        "Output file name:",
        default=default_output
    ).ask()

    if not output_file:
        return

    try:
        from collectors.apify_facebook import extract_facebook_emails_from_csv_with_social_platform

        console.print(f"\n[green]🚀 Starting Facebook email extraction...[/green]")
        console.print(f"Input: {csv_file}")
        console.print(f"Output: {output_file}")

        # Run extraction
        enhanced_df = extract_facebook_emails_from_csv_with_social_platform(
            csv_file_path=csv_file,
            apify_token=apify_token,
            output_file=output_file
        )

        # Show results
        facebook_emails = len(enhanced_df[enhanced_df['facebook_email'].notna() & (enhanced_df['facebook_email'] != '')])
        total_businesses = len(enhanced_df)

        console.print(f"\n[bold green]✅ Extraction completed![/bold green]")
        console.print(f"📧 Facebook emails extracted: {facebook_emails}")
        console.print(f"📊 Total businesses: {total_businesses}")
        console.print(f"💾 Results saved to: {output_file}")

        # Show examples if any found
        if facebook_emails > 0:
            examples = enhanced_df[enhanced_df['facebook_email'].notna() & (enhanced_df['facebook_email'] != '')][['name', 'facebook_email']].head(3)
            if not examples.empty:
                console.print("\n[bold]Examples of extracted emails:[/bold]")
                for _, row in examples.iterrows():
                    console.print(f"  • {row['name']}: {row['facebook_email']}")

    except ImportError:
        console.print("[red]Error: Facebook extraction dependencies not available.[/red]")
        console.print("Run: pip install apify-client")
    except Exception as e:
        console.print(f"[red]Error during extraction: {str(e)}[/red]")


@collect_app.callback()
def collect_callback():
    """Business data collection commands"""
    pass
