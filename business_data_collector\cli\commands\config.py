"""
Configuration management commands for the CLI
"""
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from typing import Optional, Dict, Any
import json
import os
from pathlib import Path
import questionary

console = Console()

# Create config app
config_app = typer.Typer(help="Manage CLI configuration")

# Configuration file path
CONFIG_DIR = Path.home() / ".business-cli"
CONFIG_FILE = CONFIG_DIR / "config.json"

# Default configuration
DEFAULT_CONFIG = {
    "default_radius": 10.0,
    "default_sources": ["google", "overpass", "yellowpages"],
    "default_batch_size": 25,
    "default_concurrent": 5,
    "output_format": "CSV",
    "google_api_key": "",
    "openai_api_key": ""
}


def _load_config() -> Dict[str, Any]:
    """Load configuration from file"""
    if not CONFIG_FILE.exists():
        return DEFAULT_CONFIG.copy()

    try:
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)
        # Merge with defaults to ensure all keys exist
        merged_config = DEFAULT_CONFIG.copy()
        merged_config.update(config)
        return merged_config
    except Exception as e:
        console.print(f"[red]Error loading config: {e}[/red]")
        return DEFAULT_CONFIG.copy()


def _save_config(config: Dict[str, Any]) -> bool:
    """Save configuration to file"""
    try:
        CONFIG_DIR.mkdir(exist_ok=True)
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
        return True
    except Exception as e:
        console.print(f"[red]Error saving config: {e}[/red]")
        return False


@config_app.command("show")
def show_config():
    """
    ⚙️ Show current configuration
    """
    config = _load_config()

    table = Table(title="Current CLI Configuration", show_header=True, header_style="bold yellow")
    table.add_column("Setting", style="cyan", no_wrap=True)
    table.add_column("Value", style="white")
    table.add_column("Description", style="dim")

    descriptions = {
        "default_radius": "Default search radius in miles",
        "default_sources": "Default data sources to use",
        "default_batch_size": "Default batch size for scraping",
        "default_concurrent": "Default concurrent requests",
        "output_format": "Default output format",
        "google_api_key": "Google Places API key",
        "openai_api_key": "OpenAI API key"
    }

    for key, value in config.items():
        if key.endswith('_key') and value:
            # Hide API keys for security
            display_value = f"{'*' * (len(str(value)) - 4)}{str(value)[-4:]}"
        else:
            display_value = str(value)

        table.add_row(
            key,
            display_value,
            descriptions.get(key, "")
        )

    console.print(table)
    console.print(f"\n[dim]Config file location: {CONFIG_FILE}[/dim]")


@config_app.command("set")
def set_config(
    key: str = typer.Argument(..., help="Configuration key"),
    value: str = typer.Argument(..., help="Configuration value")
):
    """
    🔧 Set a configuration value
    """
    config = _load_config()

    if key not in DEFAULT_CONFIG:
        console.print(f"[red]Unknown configuration key: {key}[/red]")
        console.print(f"[yellow]Valid keys: {', '.join(DEFAULT_CONFIG.keys())}[/yellow]")
        raise typer.Exit(1)

    # Type conversion based on default value
    default_value = DEFAULT_CONFIG[key]
    try:
        if isinstance(default_value, bool):
            config[key] = value.lower() in ('true', '1', 'yes', 'on')
        elif isinstance(default_value, (int, float)):
            config[key] = type(default_value)(value)
        elif isinstance(default_value, list):
            config[key] = [item.strip() for item in value.split(',')]
        else:
            config[key] = value
    except ValueError as e:
        console.print(f"[red]Invalid value for {key}: {e}[/red]")
        raise typer.Exit(1)

    if _save_config(config):
        console.print(f"[green]✓ Set {key} = {config[key]}[/green]")
    else:
        console.print("[red]Failed to save configuration[/red]")
        raise typer.Exit(1)


@config_app.command("reset")
def reset_config():
    """
    🔄 Reset configuration to defaults
    """
    if CONFIG_FILE.exists():
        CONFIG_FILE.unlink()
        console.print("[green]✓ Configuration reset to defaults[/green]")
    else:
        console.print("[yellow]Configuration already at defaults[/yellow]")


@config_app.command("edit")
def edit_config():
    """
    📝 Open configuration file in default editor
    """
    config = _load_config()  # Ensure config file exists
    _save_config(config)

    console.print(f"[blue]Configuration file: {CONFIG_FILE}[/blue]")
    console.print("[yellow]Open this file in your preferred text editor to make changes[/yellow]")


@config_app.command("interactive")
def interactive_config():
    """
    🎯 Interactive configuration management
    """
    console.print(Panel(
        "⚙️ Interactive Configuration\n\n"
        "Manage your CLI settings with easy-to-use menus.",
        title="Configuration Management",
        border_style="yellow"
    ))

    while True:
        choice = questionary.select(
            "What would you like to configure?",
            choices=[
                questionary.Choice("🏠 Default Search Radius", "radius"),
                questionary.Choice("📊 Default Data Sources", "sources"),
                questionary.Choice("📁 Output Format", "output"),
                questionary.Choice("🔑 API Keys", "api_keys"),
                questionary.Choice("⚡ Scraping Settings", "scraping"),
                questionary.Choice("👁️ View Current Config", "view"),
                questionary.Choice("🔄 Reset to Defaults", "reset"),
                questionary.Choice("✅ Done", "done")
            ],
            style=questionary.Style([
                ('question', 'bold'),
                ('answer', 'fg:#ff9d00 bold'),
                ('pointer', 'fg:#ff9d00 bold'),
                ('highlighted', 'fg:#ff9d00 bold'),
            ])
        ).ask()

        if choice == "radius":
            _configure_radius()
        elif choice == "sources":
            _configure_sources()
        elif choice == "output":
            _configure_output()
        elif choice == "api_keys":
            _configure_api_keys()
        elif choice == "scraping":
            _configure_scraping()
        elif choice == "view":
            show_config()
        elif choice == "reset":
            _interactive_reset()
        elif choice == "done" or choice is None:
            break


def _configure_radius():
    """Configure default search radius"""
    config = _load_config()
    current_radius = config.get("default_radius", 10.0)

    console.print(f"[blue]Current default radius: {current_radius} miles[/blue]")

    radius_choice = questionary.select(
        "Select new default radius:",
        choices=[
            questionary.Choice("🏠 1 mile (very local)", 1.0),
            questionary.Choice("🏘️ 5 miles (neighborhood)", 5.0),
            questionary.Choice("🏙️ 10 miles (city area)", 10.0),
            questionary.Choice("🌆 15 miles (metro area)", 15.0),
            questionary.Choice("🗺️ 25 miles (regional)", 25.0),
            questionary.Choice("🌍 50 miles (wide area)", 50.0),
            questionary.Choice("⚙️ Custom radius", "custom"),
            questionary.Choice("🔙 Keep current", None)
        ]
    ).ask()

    if radius_choice == "custom":
        custom_radius = questionary.text(
            "Enter custom radius in miles:",
            default=str(current_radius),
            validate=lambda x: x.replace('.', '').isdigit() or "Please enter a valid number"
        ).ask()

        if custom_radius:
            radius_choice = float(custom_radius)

    if radius_choice is not None:
        config["default_radius"] = radius_choice
        if _save_config(config):
            console.print(f"[green]✓ Default radius set to {radius_choice} miles[/green]")


def _configure_sources():
    """Configure default data sources"""
    config = _load_config()
    current_sources = config.get("default_sources", ["google", "overpass", "yellowpages"])

    console.print(f"[blue]Current default sources: {', '.join(current_sources)}[/blue]")

    source_options = [
        questionary.Choice("🏢 Google Places API (most reliable)", "google"),
        questionary.Choice("🗺️ Overpass API (free OpenStreetMap)", "overpass"),
        questionary.Choice("📞 Yellow Pages (web scraping)", "yellowpages")
    ]

    selected_sources = questionary.checkbox(
        "Select default data sources:",
        choices=source_options,
        default=current_sources
    ).ask()

    if selected_sources:
        config["default_sources"] = selected_sources
        if _save_config(config):
            console.print(f"[green]✓ Default sources set to: {', '.join(selected_sources)}[/green]")


def _configure_output():
    """Configure default output format"""
    config = _load_config()
    current_format = config.get("output_format", "CSV")

    console.print(f"[blue]Current default format: {current_format}[/blue]")

    format_choice = questionary.select(
        "Select default output format:",
        choices=[
            questionary.Choice("📊 CSV (spreadsheet format)", "CSV"),
            questionary.Choice("📋 JSON (structured data)", "JSON"),
            questionary.Choice("📈 Both CSV and JSON", "Both CSV and JSON")
        ],
        default=current_format
    ).ask()

    if format_choice:
        config["output_format"] = format_choice
        if _save_config(config):
            console.print(f"[green]✓ Default output format set to: {format_choice}[/green]")


def _configure_api_keys():
    """Configure API keys"""
    config = _load_config()

    console.print(Panel(
        "🔑 API Key Configuration\n\n"
        "Set up your API keys for data collection services.",
        title="API Keys",
        border_style="red"
    ))

    key_choice = questionary.select(
        "Which API key would you like to configure?",
        choices=[
            questionary.Choice("🏢 Google Places API Key", "google"),
            questionary.Choice("🤖 OpenAI API Key", "openai"),
            questionary.Choice("🔙 Back", None)
        ]
    ).ask()

    if key_choice == "google":
        current_key = config.get("google_api_key", "")
        display_key = f"{'*' * (len(current_key) - 4)}{current_key[-4:]}" if current_key else "Not set"
        console.print(f"[blue]Current Google API key: {display_key}[/blue]")

        new_key = questionary.password(
            "Enter Google Places API key (leave empty to keep current):"
        ).ask()

        if new_key:
            config["google_api_key"] = new_key
            if _save_config(config):
                console.print("[green]✓ Google API key updated[/green]")

    elif key_choice == "openai":
        current_key = config.get("openai_api_key", "")
        display_key = f"{'*' * (len(current_key) - 4)}{current_key[-4:]}" if current_key else "Not set"
        console.print(f"[blue]Current OpenAI API key: {display_key}[/blue]")

        new_key = questionary.password(
            "Enter OpenAI API key (leave empty to keep current):"
        ).ask()

        if new_key:
            config["openai_api_key"] = new_key
            if _save_config(config):
                console.print("[green]✓ OpenAI API key updated[/green]")


def _configure_scraping():
    """Configure scraping settings"""
    config = _load_config()

    console.print(Panel(
        "⚡ Scraping Configuration\n\n"
        "Configure batch sizes and concurrency for email scraping.",
        title="Scraping Settings",
        border_style="cyan"
    ))

    setting_choice = questionary.select(
        "Which scraping setting would you like to configure?",
        choices=[
            questionary.Choice("📦 Default Batch Size", "batch_size"),
            questionary.Choice("🔄 Default Concurrent Requests", "concurrent"),
            questionary.Choice("🔙 Back", None)
        ]
    ).ask()

    if setting_choice == "batch_size":
        current_batch = config.get("default_batch_size", 25)
        console.print(f"[blue]Current default batch size: {current_batch}[/blue]")

        batch_choice = questionary.select(
            "Select default batch size:",
            choices=[
                questionary.Choice("🐌 5 (very conservative)", 5),
                questionary.Choice("🚶 10 (conservative)", 10),
                questionary.Choice("🏃 25 (balanced)", 25),
                questionary.Choice("🏎️ 50 (aggressive)", 50),
                questionary.Choice("🚀 100 (very aggressive)", 100),
                questionary.Choice("⚙️ Custom", "custom")
            ]
        ).ask()

        if batch_choice == "custom":
            custom_batch = questionary.text(
                "Enter custom batch size:",
                default=str(current_batch),
                validate=lambda x: x.isdigit() and int(x) > 0 or "Please enter a positive number"
            ).ask()

            if custom_batch:
                batch_choice = int(custom_batch)

        if batch_choice:
            config["default_batch_size"] = batch_choice
            if _save_config(config):
                console.print(f"[green]✓ Default batch size set to {batch_choice}[/green]")

    elif setting_choice == "concurrent":
        current_concurrent = config.get("default_concurrent", 5)
        console.print(f"[blue]Current default concurrent requests: {current_concurrent}[/blue]")

        concurrent_choice = questionary.select(
            "Select default concurrent requests:",
            choices=[
                questionary.Choice("🐌 1 (very safe)", 1),
                questionary.Choice("🚶 2 (safe)", 2),
                questionary.Choice("🏃 5 (balanced)", 5),
                questionary.Choice("🏎️ 10 (aggressive)", 10),
                questionary.Choice("🚀 20 (very aggressive)", 20),
                questionary.Choice("⚙️ Custom", "custom")
            ]
        ).ask()

        if concurrent_choice == "custom":
            custom_concurrent = questionary.text(
                "Enter custom concurrent requests:",
                default=str(current_concurrent),
                validate=lambda x: x.isdigit() and int(x) > 0 or "Please enter a positive number"
            ).ask()

            if custom_concurrent:
                concurrent_choice = int(custom_concurrent)

        if concurrent_choice:
            config["default_concurrent"] = concurrent_choice
            if _save_config(config):
                console.print(f"[green]✓ Default concurrent requests set to {concurrent_choice}[/green]")


def _interactive_reset():
    """Interactive configuration reset"""
    console.print(Panel(
        "🔄 Reset Configuration\n\n"
        "This will reset all settings to their default values.",
        title="Reset Configuration",
        border_style="red"
    ))

    confirm = questionary.confirm(
        "Are you sure you want to reset all configuration to defaults?"
    ).ask()

    if confirm:
        if CONFIG_FILE.exists():
            CONFIG_FILE.unlink()
            console.print("[green]✓ Configuration reset to defaults[/green]")
        else:
            console.print("[yellow]Configuration already at defaults[/yellow]")


@config_app.callback()
def config_callback():
    """Configuration management commands"""
    pass
