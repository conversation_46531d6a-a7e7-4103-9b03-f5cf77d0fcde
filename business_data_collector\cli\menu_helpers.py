"""
Enhanced menu helpers for the Business Data Collector CLI
Provides improved arrow-key navigation and menu-driven interfaces
"""
import questionary
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.columns import Columns
from typing import List, Dict, Any, Optional
import sys
import os
from pathlib import Path
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cli.categories import CategoryManager, CategoryGroup

console = Console()


def enhanced_category_selection() -> List[str]:
    """Enhanced category selection with hierarchical menu navigation"""
    manager = CategoryManager()
    selected_categories = []

    console.print(Panel(
        "🏷️ Category Selection\n\n"
        "💡 Quick Start: Try 'Quick Presets' for common category combinations\n"
        "📂 Or browse by groups and use SPACEBAR to select individual categories\n"
        "✨ Select 'Finish Selection' when you have at least one category",
        title="Business Categories",
        border_style="cyan"
    ))

    # First, try to get user to use Quick Presets for easier selection
    use_presets = questionary.confirm(
        "Would you like to use Quick Presets for faster category selection?"
    ).ask()

    if use_presets:
        preset_categories = _select_category_presets(manager)
        if preset_categories:
            console.print(f"[green]✅ Selected {len(preset_categories)} categories from preset![/green]")
            return preset_categories

    # If no presets selected, continue with manual selection
    while True:
        # Main category selection menu
        choice = questionary.select(
            "Choose how to select categories:",
            choices=[
                questionary.Choice("📂 Browse by Category Groups", "groups"),
                questionary.Choice("🔍 Search Categories", "search"),
                questionary.Choice("📋 Select from All Categories", "all"),
                questionary.Choice("⭐ Quick Presets", "presets"),
                questionary.Choice("✅ Review Selected Categories", "review"),
                questionary.Choice("✨ Finish Selection", "finish")
            ],
            style=questionary.Style([
                ('question', 'bold'),
                ('answer', 'fg:#ff9d00 bold'),
                ('pointer', 'fg:#ff9d00 bold'),
                ('highlighted', 'fg:#ff9d00 bold'),
                ('selected', 'fg:#cc5454'),
            ])
        ).ask()

        if choice == "groups":
            new_selections = _browse_category_groups(manager)
            if new_selections:
                selected_categories.extend(new_selections)
        elif choice == "search":
            new_selections = _search_categories(manager)
            if new_selections:
                selected_categories.extend(new_selections)
        elif choice == "all":
            new_selections = _select_all_categories(manager)
            if new_selections:
                selected_categories.extend(new_selections)
        elif choice == "presets":
            new_selections = _select_category_presets(manager)
            if new_selections:
                selected_categories.extend(new_selections)
        elif choice == "review":
            selected_categories = _review_selected_categories(manager, selected_categories)
        elif choice == "finish" or choice is None:
            if not selected_categories:
                console.print("[yellow]⚠️  No categories selected yet![/yellow]")
                console.print("[dim]💡 Tip: Use 'Quick Presets' for fastest selection, or 'Browse by Category Groups'[/dim]")

                # Offer to use a default preset
                use_default = questionary.confirm(
                    "Would you like to use 'All Food & Dining' as a quick start?"
                ).ask()

                if use_default:
                    selected_categories = ["restaurants", "cafes_coffee", "bars_pubs", "fast_food"]
                    console.print(f"[green]✅ Selected {len(selected_categories)} Food & Dining categories![/green]")
                    break
                else:
                    continue
            break

        # Remove duplicates
        selected_categories = list(set(selected_categories))

        # Show current selection count
        if selected_categories:
            console.print(f"[green]Currently selected: {len(selected_categories)} categories[/green]")

    return selected_categories


def _browse_category_groups(manager: CategoryManager) -> List[str]:
    """Browse categories by groups with hierarchical navigation"""
    while True:
        # Show category groups with counts
        group_choices = []
        for group in CategoryGroup:
            categories = manager.get_categories_by_group(group)
            count = len(categories)
            group_choices.append(
                questionary.Choice(
                    f"{group.value} ({count} categories)",
                    group
                )
            )

        selected_group = questionary.select(
            "Select a category group to explore:",
            choices=group_choices + [questionary.Choice("🔙 Back to Main Menu", None)]
        ).ask()

        if selected_group is None:
            return []

        # Show categories in the selected group
        categories = manager.get_categories_by_group(selected_group)
        category_choices = [
            questionary.Choice(
                f"{cat.name} - {cat.description}",
                next(cat_id for cat_id, cat_data in manager.get_all_categories().items() if cat_data == cat)
            )
            for cat in categories
        ]

        selections = questionary.checkbox(
            f"Select categories from {selected_group.value}:\n(Use SPACEBAR to select/deselect, ENTER to confirm)",
            choices=category_choices
        ).ask()

        if not selections:
            console.print("[yellow]No categories selected from this group.[/yellow]")
            # Ask if they want to try another group or go back
            try_again = questionary.confirm("Try another category group?").ask()
            if not try_again:
                return []
        else:
            console.print(f"[green]✅ Selected {len(selections)} categories from {selected_group.value}[/green]")
            return selections


def _search_categories(manager: CategoryManager) -> List[str]:
    """Search categories with interactive results"""
    search_term = questionary.text(
        "Enter search term (keywords, category name, etc.):",
        validate=lambda x: len(x.strip()) > 0 or "Search term cannot be empty"
    ).ask()
    
    if not search_term:
        return []
    
    results = manager.search_categories(search_term)
    
    if not results:
        console.print(f"[yellow]No categories found for '{search_term}'[/yellow]")
        return []
    
    console.print(f"[green]Found {len(results)} categories matching '{search_term}':[/green]")
    
    # Create choices from search results
    result_choices = [
        questionary.Choice(
            f"{cat.name} - {cat.description}",
            next(cat_id for cat_id, cat_data in manager.get_all_categories().items() if cat_data == cat)
        )
        for cat in results
    ]
    
    selections = questionary.checkbox(
        "Select categories from search results:",
        choices=result_choices
    ).ask()
    
    return selections or []


def _select_all_categories(manager: CategoryManager) -> List[str]:
    """Select from all categories with pagination"""
    all_categories = manager.get_all_categories()
    
    # Group categories for better display
    category_choices = [
        questionary.Choice(
            f"{cat.name} - {cat.description}",
            cat_id
        )
        for cat_id, cat in all_categories.items()
    ]
    
    # Sort alphabetically by title
    category_choices.sort(key=lambda x: str(x.title) if hasattr(x, 'title') and x.title else "")
    
    selections = questionary.checkbox(
        f"Select from all {len(category_choices)} categories:",
        choices=category_choices
    ).ask()
    
    return selections or []


def _select_category_presets(manager: CategoryManager) -> List[str]:
    """Quick category presets for common use cases"""
    presets = {
        "All Food & Dining": ["restaurants", "cafes_coffee", "bars_pubs", "fast_food"],
        "All Retail & Shopping": ["retail_stores", "clothing_fashion", "electronics", "grocery_supermarket"],
        "All Health & Medical": ["medical_services", "dentists", "pharmacies", "veterinary"],
        "All Professional Services": ["law_firms", "real_estate_agencies", "accounting_firms", "financial_advisors"],
        "All Beauty & Wellness": ["beauty_salons", "spas_wellness", "gyms_fitness"],
        "Local Services": ["electricians", "plumbers", "locksmiths", "florists", "laundromats"],
        "Financial Services": ["banks", "insurance_agencies", "accounting_firms", "financial_advisors", "atms"]
    }
    
    preset_choices = [
        questionary.Choice(f"{name} ({len(categories)} categories)", categories)
        for name, categories in presets.items()
    ]
    
    selected_preset = questionary.select(
        "Choose a category preset:",
        choices=preset_choices + [questionary.Choice("🔙 Back", None)]
    ).ask()
    
    if selected_preset is None:
        return []
    
    # Show what's included and confirm
    preset_name = next(name for name, cats in presets.items() if cats == selected_preset)
    category_names = []
    for cat_id in selected_preset:
        cat = manager.get_category(cat_id)
        if cat:
            category_names.append(cat.name)
    
    console.print(f"\n[cyan]{preset_name} includes:[/cyan]")
    for name in category_names:
        console.print(f"  • {name}")
    
    confirm = questionary.confirm(f"Add all {len(selected_preset)} categories from this preset?").ask()

    if confirm:
        console.print(f"[green]✅ Added {len(selected_preset)} categories from {preset_name}![/green]")
        return selected_preset
    else:
        console.print("[yellow]Preset not added.[/yellow]")
        return []


def _review_selected_categories(manager: CategoryManager, selected_categories: List[str]) -> List[str]:
    """Review and modify selected categories"""
    if not selected_categories:
        console.print("[yellow]No categories selected yet.[/yellow]")
        return selected_categories
    
    # Show current selections
    console.print(f"\n[bold cyan]Currently Selected Categories ({len(selected_categories)}):[/bold cyan]")
    
    table = Table(show_header=True, header_style="bold blue")
    table.add_column("Category", style="cyan")
    table.add_column("Description", style="white")
    
    for cat_id in selected_categories:
        cat = manager.get_category(cat_id)
        if cat:
            table.add_row(cat.name, cat.description)
    
    console.print(table)
    
    # Options for modification
    choice = questionary.select(
        "What would you like to do?",
        choices=[
            questionary.Choice("➕ Add more categories", "add"),
            questionary.Choice("➖ Remove categories", "remove"),
            questionary.Choice("🗑️ Clear all selections", "clear"),
            questionary.Choice("✅ Keep current selection", "keep")
        ]
    ).ask()
    
    if choice == "remove":
        # Show categories for removal
        remove_choices = []
        for cat_id in selected_categories:
            cat = manager.get_category(cat_id)
            if cat:
                remove_choices.append(questionary.Choice(cat.name, cat_id))
        
        to_remove = questionary.checkbox(
            "Select categories to remove:",
            choices=remove_choices
        ).ask()
        
        if to_remove:
            for cat_id in to_remove:
                selected_categories.remove(cat_id)
            console.print(f"[green]Removed {len(to_remove)} categories[/green]")
    
    elif choice == "clear":
        confirm = questionary.confirm("Are you sure you want to clear all selections?").ask()
        if confirm:
            selected_categories = []
            console.print("[green]All selections cleared[/green]")
    
    return selected_categories


def enhanced_location_selection() -> Optional[Dict[str, Any]]:
    """Enhanced location selection with presets and recent locations"""
    from cli.location import LocationService
    
    location_service = LocationService()
    
    console.print(Panel(
        "📍 Location Selection\n\n"
        "Choose your search location using the menu options below.",
        title="Search Location",
        border_style="green"
    ))
    
    choice = questionary.select(
        "How would you like to specify the location?",
        choices=[
            questionary.Choice("🏙️ Major Cities", "cities"),
            questionary.Choice("📝 Enter Custom Location", "custom"),
            questionary.Choice("🗺️ Enter Coordinates", "coordinates"),
            questionary.Choice("📍 Recent Locations", "recent")
        ]
    ).ask()
    
    if choice == "cities":
        return _select_major_city(location_service)
    elif choice == "custom":
        return _enter_custom_location(location_service)
    elif choice == "coordinates":
        return _enter_coordinates(location_service)
    elif choice == "recent":
        return _select_recent_location(location_service)
    
    return None


def _select_major_city(location_service) -> Optional[Dict[str, Any]]:
    """Select from major cities"""
    major_cities = [
        "New York, NY", "Los Angeles, CA", "Chicago, IL", "Houston, TX",
        "Phoenix, AZ", "Philadelphia, PA", "San Antonio, TX", "San Diego, CA",
        "Dallas, TX", "San Jose, CA", "Austin, TX", "Jacksonville, FL",
        "Fort Worth, TX", "Columbus, OH", "Charlotte, NC", "San Francisco, CA",
        "Indianapolis, IN", "Seattle, WA", "Denver, CO", "Washington, DC",
        "Boston, MA", "El Paso, TX", "Nashville, TN", "Detroit, MI",
        "Oklahoma City, OK", "Portland, OR", "Las Vegas, NV", "Memphis, TN",
        "Louisville, KY", "Baltimore, MD", "Milwaukee, WI", "Albuquerque, NM",
        "Tucson, AZ", "Fresno, CA", "Sacramento, CA", "Kansas City, MO",
        "Mesa, AZ", "Atlanta, GA", "Omaha, NE", "Colorado Springs, CO",
        "Raleigh, NC", "Miami, FL", "Virginia Beach, VA", "Oakland, CA",
        "Minneapolis, MN", "Tulsa, OK", "Arlington, TX", "Tampa, FL",
        "New Orleans, LA", "Wichita, KS", "Cleveland, OH", "Bakersfield, CA",
        "Aurora, CO", "Anaheim, CA", "Honolulu, HI", "Santa Ana, CA",
        "Riverside, CA", "Corpus Christi, TX", "Lexington, KY", "Henderson, NV",
        "Stockton, CA", "Saint Paul, MN", "St. Louis, MO", "Cincinnati, OH",
        "Pittsburgh, PA"
    ]
    
    selected_city = questionary.select(
        "Select a major city:",
        choices=sorted(major_cities) + ["🔙 Back"]
    ).ask()
    
    if selected_city == "🔙 Back" or not selected_city:
        return None
    
    # Geocode the selected city
    console.print(f"[yellow]Looking up location: {selected_city}...[/yellow]")
    location = location_service.geocode_location(selected_city)
    
    if location:
        console.print(f"[green]Found: {location.formatted_address}[/green]")
        return {
            'type': 'geocoded',
            'latitude': location.latitude,
            'longitude': location.longitude,
            'name': location.formatted_address,
            'city': location.city,
            'state': location.state,
            'country': location.country
        }
    else:
        console.print(f"[red]Could not find location: {selected_city}[/red]")
        return None


def _enter_custom_location(location_service) -> Optional[Dict[str, Any]]:
    """Enter custom location with validation"""
    location_input = questionary.text(
        "Enter location (city name, 'City, State', or address):",
        validate=lambda x: len(x.strip()) > 0 or "Location cannot be empty"
    ).ask()
    
    if not location_input:
        return None
    
    # Parse and geocode
    parsed = location_service.parse_location_input(location_input)
    
    if parsed['type'] == 'invalid':
        console.print(f"[red]Error: {parsed['error']}[/red]")
        return None
    
    if parsed['type'] == 'coordinates':
        console.print(f"[green]Using coordinates: {parsed['latitude']}, {parsed['longitude']}[/green]")
        return {
            'type': 'coordinates',
            'latitude': parsed['latitude'],
            'longitude': parsed['longitude'],
            'name': f"{parsed['latitude']}, {parsed['longitude']}"
        }
    else:
        console.print(f"[yellow]Looking up location: {parsed['query']}...[/yellow]")
        location = location_service.geocode_location(parsed['query'])
        
        if location:
            console.print(f"[green]Found: {location.formatted_address}[/green]")
            return {
                'type': 'geocoded',
                'latitude': location.latitude,
                'longitude': location.longitude,
                'name': location.formatted_address,
                'city': location.city,
                'state': location.state,
                'country': location.country
            }
        else:
            console.print(f"[red]Could not find location: {parsed['query']}[/red]")
            return None


def _enter_coordinates(location_service) -> Optional[Dict[str, Any]]:
    """Enter coordinates directly"""
    coords_input = questionary.text(
        "Enter coordinates (format: latitude,longitude):",
        validate=lambda x: ',' in x or "Please use format: latitude,longitude"
    ).ask()
    
    if not coords_input:
        return None
    
    try:
        lat_str, lng_str = coords_input.split(',')
        latitude = float(lat_str.strip())
        longitude = float(lng_str.strip())
        
        console.print(f"[green]Using coordinates: {latitude}, {longitude}[/green]")
        return {
            'type': 'coordinates',
            'latitude': latitude,
            'longitude': longitude,
            'name': f"{latitude}, {longitude}"
        }
    except (ValueError, IndexError):
        console.print("[red]Invalid coordinate format. Please use: latitude,longitude[/red]")
        return None


def _select_recent_location(location_service) -> Optional[Dict[str, Any]]:
    """Select from recent locations (placeholder for future implementation)"""
    console.print("[yellow]Recent locations feature coming soon![/yellow]")
    console.print("[dim]For now, please use one of the other location options.[/dim]")
    return None


def browse_csv_files() -> Optional[str]:
    """Browse and select CSV files from the file system"""
    console.print(Panel(
        "📁 CSV File Browser\n\n"
        "Browse your file system to select a CSV file for processing.",
        title="File Selection",
        border_style="blue"
    ))

    current_dir = Path.cwd()

    while True:
        # Get all directories and CSV files in current directory
        try:
            items = []

            # Add parent directory option (unless we're at root)
            if current_dir.parent != current_dir:
                items.append(questionary.Choice("📁 .. (Parent Directory)", ".."))

            # Add directories
            directories = [d for d in current_dir.iterdir() if d.is_dir() and not d.name.startswith('.')]
            for directory in sorted(directories):
                items.append(questionary.Choice(f"📁 {directory.name}/", directory))

            # Add CSV files
            csv_files = [f for f in current_dir.iterdir() if f.is_file() and f.suffix.lower() == '.csv']
            for csv_file in sorted(csv_files):
                # Get file size and modification time
                try:
                    stat = csv_file.stat()
                    size = f"{stat.st_size / 1024:.1f} KB"
                    modified = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")

                    # Try to count rows
                    try:
                        import pandas as pd
                        df = pd.read_csv(csv_file)
                        row_count = f"{len(df)} rows"
                    except:
                        row_count = "? rows"

                    display_name = f"📄 {csv_file.name} ({row_count}, {size}, {modified})"
                    items.append(questionary.Choice(display_name, csv_file))
                except:
                    items.append(questionary.Choice(f"📄 {csv_file.name}", csv_file))

            # Add navigation options
            items.extend([
                questionary.Choice("🏠 Go to Home Directory", "home"),
                questionary.Choice("📂 Go to Output Directory", "output"),
                questionary.Choice("💾 Go to Desktop", "desktop"),
                questionary.Choice("📝 Enter File Path Manually", "manual"),
                questionary.Choice("🔙 Cancel", None)
            ])

            if not csv_files and not directories:
                console.print(f"[yellow]No CSV files or directories found in {current_dir}[/yellow]")

            console.print(f"\n[blue]Current directory: {current_dir}[/blue]")
            if csv_files:
                console.print(f"[green]Found {len(csv_files)} CSV files[/green]")

            choice = questionary.select(
                "Select a CSV file or navigate:",
                choices=items,
                style=questionary.Style([
                    ('question', 'bold'),
                    ('answer', 'fg:#ff9d00 bold'),
                    ('pointer', 'fg:#ff9d00 bold'),
                    ('highlighted', 'fg:#ff9d00 bold'),
                ])
            ).ask()

            if choice is None:
                return None
            elif choice == "..":
                current_dir = current_dir.parent
            elif choice == "home":
                current_dir = Path.home()
            elif choice == "output":
                output_dir = Path.cwd() / "output"
                if output_dir.exists():
                    current_dir = output_dir
                else:
                    console.print("[yellow]Output directory not found[/yellow]")
            elif choice == "desktop":
                desktop_dir = Path.home() / "Desktop"
                if desktop_dir.exists():
                    current_dir = desktop_dir
                else:
                    console.print("[yellow]Desktop directory not found[/yellow]")
            elif choice == "manual":
                return _manual_file_input()
            elif isinstance(choice, Path):
                if choice.is_dir():
                    current_dir = choice
                else:
                    # It's a CSV file
                    console.print(f"[green]Selected: {choice}[/green]")

                    # Confirm selection and show file info
                    if _confirm_csv_selection(choice):
                        return str(choice)
                    # If not confirmed, continue browsing

        except PermissionError:
            console.print("[red]Permission denied accessing this directory[/red]")
            current_dir = current_dir.parent
        except Exception as e:
            console.print(f"[red]Error browsing directory: {e}[/red]")
            return None


def _manual_file_input() -> Optional[str]:
    """Manual file path input with validation"""
    file_path = questionary.path(
        "Enter the full path to your CSV file:",
        validate=lambda x: Path(x).exists() and Path(x).suffix.lower() == '.csv' or "Please enter a valid CSV file path"
    ).ask()

    if file_path and Path(file_path).exists():
        return file_path
    return None


def _confirm_csv_selection(csv_file: Path) -> bool:
    """Confirm CSV file selection and show preview"""
    try:
        import pandas as pd

        # Read first few rows for preview
        df = pd.read_csv(csv_file, nrows=5)

        console.print(Panel(
            f"[bold]File:[/bold] {csv_file.name}\n"
            f"[bold]Path:[/bold] {csv_file}\n"
            f"[bold]Size:[/bold] {csv_file.stat().st_size / 1024:.1f} KB\n"
            f"[bold]Columns:[/bold] {len(df.columns)}\n"
            f"[bold]Sample columns:[/bold] {', '.join(df.columns[:5])}{'...' if len(df.columns) > 5 else ''}",
            title="File Preview",
            border_style="green"
        ))

        # Show sample data
        console.print("\n[bold]Sample data (first 3 rows):[/bold]")
        console.print(df.head(3).to_string(index=False, max_cols=8))

        return questionary.confirm(f"Use this CSV file for processing?").ask() or False

    except Exception as e:
        console.print(f"[red]Error reading CSV file: {e}[/red]")
        return questionary.confirm(f"File appears to have issues. Use anyway?").ask() or False
