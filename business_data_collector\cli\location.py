"""
Location services for geocoding and coordinate management
"""
from typing import <PERSON><PERSON>, Optional, Dict, Any
from dataclasses import dataclass
import re
from geopy.geocoders import Nominatim, GoogleV3
from geopy.exc import GeocoderTimedOut, GeocoderServiceError
import time


@dataclass
class Location:
    """Represents a geographic location"""
    name: str
    latitude: float
    longitude: float
    formatted_address: str
    country: str = ""
    state: str = ""
    city: str = ""


class LocationService:
    """Service for geocoding and location operations"""
    
    def __init__(self, google_api_key: Optional[str] = None):
        """
        Initialize location service with geocoding providers
        
        Args:
            google_api_key: Optional Google Maps API key for more accurate geocoding
        """
        # Primary geocoder (free)
        self.nominatim = Nominatim(user_agent="business-data-collector")
        
        # Secondary geocoder (requires API key but more accurate)
        self.google_geocoder = None
        if google_api_key:
            self.google_geocoder = GoogleV3(api_key=google_api_key)
    
    def geocode_location(self, location_query: str, use_google: bool = False) -> Optional[Location]:
        """
        Convert a location string to coordinates
        
        Args:
            location_query: Location string (e.g., "Pittsburgh, PA", "New York City")
            use_google: Whether to use Google geocoding (requires API key)
            
        Returns:
            Location object with coordinates and details, or None if not found
        """
        # Clean and validate input
        location_query = location_query.strip()
        if not location_query:
            return None
        
        # Try Google geocoder first if available and requested
        if use_google and self.google_geocoder:
            try:
                result = self._geocode_with_google(location_query)
                if result:
                    return result
            except Exception as e:
                print(f"Google geocoding failed: {e}")
        
        # Fall back to Nominatim (OpenStreetMap)
        try:
            return self._geocode_with_nominatim(location_query)
        except Exception as e:
            print(f"Nominatim geocoding failed: {e}")
            return None
    
    def _geocode_with_nominatim(self, location_query: str) -> Optional[Location]:
        """Geocode using Nominatim (OpenStreetMap)"""
        try:
            # Add a small delay to be respectful to the service
            time.sleep(1)
            
            location = self.nominatim.geocode(
                location_query,
                exactly_one=True,
                timeout=10,
                addressdetails=True
            )
            
            if not location:
                return None
            
            # Extract address components
            address = location.raw.get('address', {})
            
            return Location(
                name=location_query,
                latitude=float(location.latitude),
                longitude=float(location.longitude),
                formatted_address=location.address,
                country=address.get('country', ''),
                state=address.get('state', ''),
                city=address.get('city', address.get('town', address.get('village', '')))
            )
            
        except (GeocoderTimedOut, GeocoderServiceError) as e:
            print(f"Geocoding service error: {e}")
            return None
    
    def _geocode_with_google(self, location_query: str) -> Optional[Location]:
        """Geocode using Google Maps API"""
        try:
            location = self.google_geocoder.geocode(
                location_query,
                exactly_one=True,
                timeout=10
            )
            
            if not location:
                return None
            
            # Extract address components from Google's response
            address_components = location.raw.get('address_components', [])
            country = ""
            state = ""
            city = ""
            
            for component in address_components:
                types = component.get('types', [])
                if 'country' in types:
                    country = component.get('long_name', '')
                elif 'administrative_area_level_1' in types:
                    state = component.get('long_name', '')
                elif 'locality' in types:
                    city = component.get('long_name', '')
            
            return Location(
                name=location_query,
                latitude=float(location.latitude),
                longitude=float(location.longitude),
                formatted_address=location.address,
                country=country,
                state=state,
                city=city
            )
            
        except (GeocoderTimedOut, GeocoderServiceError) as e:
            print(f"Google geocoding service error: {e}")
            return None
    
    def validate_coordinates(self, latitude: float, longitude: float) -> bool:
        """
        Validate latitude and longitude values
        
        Args:
            latitude: Latitude value
            longitude: Longitude value
            
        Returns:
            True if coordinates are valid, False otherwise
        """
        return (-90 <= latitude <= 90) and (-180 <= longitude <= 180)
    
    def miles_to_meters(self, miles: float) -> int:
        """
        Convert miles to meters
        
        Args:
            miles: Distance in miles
            
        Returns:
            Distance in meters (rounded to nearest integer)
        """
        return round(miles * 1609.344)
    
    def meters_to_miles(self, meters: int) -> float:
        """
        Convert meters to miles
        
        Args:
            meters: Distance in meters
            
        Returns:
            Distance in miles (rounded to 2 decimal places)
        """
        return round(meters / 1609.344, 2)
    
    def parse_location_input(self, location_input: str) -> Dict[str, Any]:
        """
        Parse various location input formats
        
        Supports:
        - City names: "Pittsburgh", "New York City"
        - City, State: "Pittsburgh, PA", "Los Angeles, CA"
        - Coordinates: "40.4406,-79.9959" or "40.4406, -79.9959"
        
        Args:
            location_input: Location string in various formats
            
        Returns:
            Dictionary with parsed location information
        """
        location_input = location_input.strip()
        
        # Check if input looks like coordinates (lat,lng)
        coord_pattern = r'^(-?\d+\.?\d*),\s*(-?\d+\.?\d*)$'
        coord_match = re.match(coord_pattern, location_input)
        
        if coord_match:
            lat = float(coord_match.group(1))
            lng = float(coord_match.group(2))
            
            if self.validate_coordinates(lat, lng):
                return {
                    'type': 'coordinates',
                    'latitude': lat,
                    'longitude': lng,
                    'original_input': location_input
                }
            else:
                return {
                    'type': 'invalid',
                    'error': 'Invalid coordinate values',
                    'original_input': location_input
                }
        
        # Otherwise treat as location name
        return {
            'type': 'location_name',
            'query': location_input,
            'original_input': location_input
        }
    
    def get_location_suggestions(self, partial_query: str, limit: int = 5) -> list:
        """
        Get location suggestions for autocomplete
        
        Args:
            partial_query: Partial location string
            limit: Maximum number of suggestions
            
        Returns:
            List of suggested location names
        """
        if len(partial_query) < 3:
            return []
        
        try:
            # Use Nominatim search for suggestions
            time.sleep(0.5)  # Be respectful to the service
            
            results = self.nominatim.geocode(
                partial_query,
                exactly_one=False,
                limit=limit,
                timeout=5
            )
            
            if not results:
                return []
            
            suggestions = []
            for result in results:
                # Extract meaningful location name
                address = result.raw.get('address', {})
                display_name = result.raw.get('display_name', '')
                
                # Try to create a clean suggestion
                city = address.get('city', address.get('town', address.get('village', '')))
                state = address.get('state', '')
                country = address.get('country', '')
                
                if city and state:
                    suggestion = f"{city}, {state}"
                elif city and country:
                    suggestion = f"{city}, {country}"
                else:
                    # Fall back to display name, but clean it up
                    parts = display_name.split(',')
                    suggestion = ', '.join(parts[:2]) if len(parts) >= 2 else parts[0]
                
                if suggestion and suggestion not in suggestions:
                    suggestions.append(suggestion)
            
            return suggestions[:limit]
            
        except Exception as e:
            print(f"Error getting location suggestions: {e}")
            return []


def create_location_service(google_api_key: Optional[str] = None) -> LocationService:
    """
    Factory function to create a LocationService instance
    
    Args:
        google_api_key: Optional Google Maps API key
        
    Returns:
        LocationService instance
    """
    return LocationService(google_api_key=google_api_key)
