"""
Configuration settings for the business data collector.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Pittsburgh coordinates
PITTSBURGH_LAT = 40.440624
PITTSBURGH_LNG = -79.995888

# Search radius in meters (10 miles = 16093 meters)
SEARCH_RADIUS_METERS = 16093

# API Keys
GOOGLE_PLACES_API_KEY = os.getenv('GOOGLE_PLACES_API_KEY', 'AIzaSyDlJOoQPoR3DTkP020M_5BnEq8JPP2tQKw')
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '********************************************************************************************************************************************************************')  # Required for Yellow Pages LLM extraction
GROQ_API_KEY = os.getenv('GROQ_API_KEY', '********************************************************')  # Required for Yellow Pages LLM extraction with Groq

# Rate limiting settings
GOOGLE_PLACES_DELAY = 1.0  # seconds between requests
YELLOW_PAGES_DELAY = 0.5   # seconds between requests (reduced for better performance)
OVERPASS_DELAY = 0.5       # seconds between requests

# Google Places API settings
GOOGLE_PLACES_TYPES = [
    'restaurant',
    'store',
    'establishment',
    'food',
    'lodging',
    'health',
    'finance',
    'beauty_salon',
    'car_repair',
    'gym',
    'laundry',
    'lawyer',
    'real_estate_agency',
    'veterinary_care'
]

# Overpass API settings
OVERPASS_URL = 'https://overpass-api.de/api/interpreter'
OVERPASS_AMENITIES = [
    'restaurant',
    'cafe',
    'bar',
    'pub',
    'fast_food',
    'bank',
    'pharmacy',
    'hospital',
    'dentist',
    'veterinary',
    'fuel',
    'car_repair',
    'car_wash',
    'parking',
    'post_office',
    'library',
    'school',
    'university',
    'place_of_worship',
    'theatre',
    'cinema',
    'nightclub',
    'casino',
    'marketplace'
]

OVERPASS_SHOPS = [
    'supermarket',
    'convenience',
    'clothes',
    'shoes',
    'electronics',
    'furniture',
    'hardware',
    'florist',
    'bakery',
    'butcher',
    'greengrocer',
    'hairdresser',
    'beauty',
    'jewelry',
    'books',
    'sports',
    'toys',
    'gift',
    'pet',
    'bicycle',
    'car',
    'motorcycle',
    'mobile_phone',
    'computer',
    'music',
    'video',
    'art',
    'antiques',
    'second_hand'
]

# Corporate chains to exclude (partial list)
CORPORATE_BLACKLIST = [
    'mcdonalds',
    'burger king',
    'subway',
    'starbucks',
    'walmart',
    'target',
    'home depot',
    'lowes',
    'cvs',
    'walgreens',
    'rite aid',
    'best buy',
    'staples',
    'office depot',
    'petsmart',
    'petco',
    'autozone',
    'advance auto parts',
    'jiffy lube',
    'valvoline instant oil change',
    'pizza hut',
    'dominos',
    'papa johns',
    'kfc',
    'taco bell',
    'wendys',
    'arbys',
    'dairy queen',
    'sonic',
    'chipotle',
    'panera bread',
    'dunkin',
    'tim hortons',
    'brueggers',
    'bruegger\'s bagels',
    'einstein bros',
    'caribou coffee',
    'peet\'s coffee',
    'the coffee bean & tea leaf',
    'seattle\'s best',
    'applebees',
    'olive garden',
    'red lobster',
    'outback steakhouse',
    'chilis',
    'tgi fridays',
    'ihop',
    'dennys',
    'cracker barrel',
    'golden corral',
    'buffalo wild wings',
    'hooters',
    'dave and busters',
    'chuck e cheese',
    'gamestop',
    'radioshack',
    'blockbuster',
    'family dollar',
    'dollar general',
    'dollar tree',
    '7-eleven',
    'circle k',
    'wawa',
    'sheetz',
    'speedway',
    'marathon',
    'shell',
    'exxon',
    'mobil',
    'bp',
    'chevron',
    'texaco',
    'sunoco',
    'citgo',
    'valero',
    'phillips 66',
    'conoco',
    'holiday inn',
    'marriott',
    'hilton',
    'hyatt',
    'sheraton',
    'westin',
    'doubletree',
    'embassy suites',
    'hampton inn',
    'courtyard',
    'fairfield inn',
    'residence inn',
    'springhill suites',
    'towneplace suites',
    'homewood suites',
    'extended stay',
    'la quinta',
    'comfort inn',
    'quality inn',
    'sleep inn',
    'econo lodge',
    'super 8',
    'motel 6',
    'red roof inn',
    'days inn',
    'ramada',
    'wyndham',
    'best western',
    'choice hotels'
]

# Output settings
OUTPUT_DIR = 'output'
OUTPUT_FILENAME = 'pittsburgh_businesses.csv'

# Logging settings
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
