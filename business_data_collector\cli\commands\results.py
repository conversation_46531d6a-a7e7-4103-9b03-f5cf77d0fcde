"""
Results management commands for the CLI
"""
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from typing import Optional, List
import os
import pandas as pd
from datetime import datetime
from pathlib import Path
import questionary

console = Console()

# Create results app
results_app = typer.Typer(help="View and manage collection results")

# Results directory
RESULTS_DIR = Path("output")


@results_app.command("list")
def list_results():
    """
    📈 List all previous collection results
    """
    if not RESULTS_DIR.exists():
        console.print("[yellow]No results directory found[/yellow]")
        return

    # Find CSV files in results directory
    csv_files = list(RESULTS_DIR.glob("*.csv"))
    scraped_files = list(Path(".").glob("scraped_*.csv"))

    all_files = csv_files + scraped_files

    if not all_files:
        console.print("[yellow]No result files found[/yellow]")
        return

    table = Table(title="Collection Results", show_header=True, header_style="bold magenta")
    table.add_column("File", style="cyan", no_wrap=True)
    table.add_column("Type", style="yellow")
    table.add_column("Size", style="white")
    table.add_column("Modified", style="dim")
    table.add_column("Records", style="green")

    for file_path in sorted(all_files, key=lambda x: x.stat().st_mtime, reverse=True):
        try:
            stat = file_path.stat()
            size = f"{stat.st_size / 1024:.1f} KB"
            modified = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")

            # Determine file type
            if "scraped" in file_path.name:
                file_type = "Email Scraping"
            elif "businesses" in file_path.name:
                file_type = "Business Collection"
            else:
                file_type = "Unknown"

            # Count records
            try:
                df = pd.read_csv(file_path)
                record_count = str(len(df))
            except:
                record_count = "Error"

            table.add_row(
                file_path.name,
                file_type,
                size,
                modified,
                record_count
            )
        except Exception as e:
            console.print(f"[red]Error reading {file_path.name}: {e}[/red]")

    console.print(table)


@results_app.command("show")
def show_result(
    filename: str = typer.Argument(..., help="Result filename to display")
):
    """
    📊 Show details of a specific result file
    """
    # Look for file in results directory or current directory
    file_path = None
    if (RESULTS_DIR / filename).exists():
        file_path = RESULTS_DIR / filename
    elif Path(filename).exists():
        file_path = Path(filename)
    else:
        console.print(f"[red]File not found: {filename}[/red]")
        console.print("[yellow]Use 'results list' to see available files[/yellow]")
        raise typer.Exit(1)

    try:
        df = pd.read_csv(file_path)

        console.print(Panel(
            f"[bold]File:[/bold] {file_path.name}\n"
            f"[bold]Records:[/bold] {len(df)}\n"
            f"[bold]Columns:[/bold] {len(df.columns)}\n"
            f"[bold]Size:[/bold] {file_path.stat().st_size / 1024:.1f} KB",
            title="Result Details",
            border_style="blue"
        ))

        # Show column info
        console.print("\n[bold]Columns:[/bold]")
        for col in df.columns:
            non_null = df[col].notna().sum()
            console.print(f"  • {col}: {non_null}/{len(df)} non-null values")

        # Show sample data
        console.print("\n[bold]Sample Data (first 5 rows):[/bold]")
        console.print(df.head().to_string(index=False))

    except Exception as e:
        console.print(f"[red]Error reading file: {e}[/red]")


@results_app.command("clean")
def clean_results():
    """
    🧹 Clean up old result files
    """
    if not RESULTS_DIR.exists():
        console.print("[yellow]No results directory found[/yellow]")
        return

    # Find old files (older than 30 days)
    import time
    current_time = time.time()
    old_files = []

    for file_path in RESULTS_DIR.glob("*.csv"):
        if current_time - file_path.stat().st_mtime > 30 * 24 * 3600:  # 30 days
            old_files.append(file_path)

    if not old_files:
        console.print("[green]No old files to clean up[/green]")
        return

    console.print(f"[yellow]Found {len(old_files)} files older than 30 days[/yellow]")
    for file_path in old_files:
        console.print(f"  • {file_path.name}")

    if typer.confirm("Delete these files?"):
        for file_path in old_files:
            file_path.unlink()
        console.print(f"[green]✓ Deleted {len(old_files)} old files[/green]")
    else:
        console.print("[yellow]Cleanup cancelled[/yellow]")


@results_app.command("interactive")
def interactive_results():
    """
    🎯 Interactive results management
    """
    console.print(Panel(
        "📈 Interactive Results Management\n\n"
        "Browse, analyze, and manage your collection results.",
        title="Results Management",
        border_style="magenta"
    ))

    while True:
        choice = questionary.select(
            "What would you like to do?",
            choices=[
                questionary.Choice("📋 Browse Results", "browse"),
                questionary.Choice("📊 View Result Details", "details"),
                questionary.Choice("📈 Compare Results", "compare"),
                questionary.Choice("📁 Export Results", "export"),
                questionary.Choice("🧹 Clean Old Files", "clean"),
                questionary.Choice("✅ Done", "done")
            ],
            style=questionary.Style([
                ('question', 'bold'),
                ('answer', 'fg:#ff9d00 bold'),
                ('pointer', 'fg:#ff9d00 bold'),
                ('highlighted', 'fg:#ff9d00 bold'),
            ])
        ).ask()

        if choice == "browse":
            _browse_results()
        elif choice == "details":
            _view_result_details()
        elif choice == "compare":
            _compare_results()
        elif choice == "export":
            _export_results()
        elif choice == "clean":
            _interactive_clean()
        elif choice == "done" or choice is None:
            break


def _get_result_files() -> List[Path]:
    """Get all result files"""
    csv_files = []

    if RESULTS_DIR.exists():
        csv_files.extend(RESULTS_DIR.glob("*.csv"))

    # Also check current directory for scraped files
    csv_files.extend(Path(".").glob("scraped_*.csv"))
    csv_files.extend(Path(".").glob("*_contacts_*.csv"))
    csv_files.extend(Path(".").glob("*_emails_*.csv"))

    return sorted(csv_files, key=lambda x: x.stat().st_mtime, reverse=True)


def _browse_results():
    """Browse results with interactive selection"""
    files = _get_result_files()

    if not files:
        console.print("[yellow]No result files found[/yellow]")
        return

    console.print(f"[green]Found {len(files)} result files:[/green]\n")

    # Create choices with file info
    file_choices = []
    for file_path in files:
        try:
            stat = file_path.stat()
            size = f"{stat.st_size / 1024:.1f} KB"
            modified = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")

            # Count records
            try:
                df = pd.read_csv(file_path)
                record_count = len(df)
            except:
                record_count = "?"

            # Determine file type
            if "scraped" in file_path.name or "emails" in file_path.name:
                file_type = "📧"
            elif "contacts" in file_path.name:
                file_type = "📞"
            elif "businesses" in file_path.name:
                file_type = "🏢"
            else:
                file_type = "📄"

            choice_text = f"{file_type} {file_path.name} ({record_count} records, {size}, {modified})"
            file_choices.append(questionary.Choice(choice_text, file_path))

        except Exception as e:
            console.print(f"[red]Error reading {file_path.name}: {e}[/red]")

    if not file_choices:
        console.print("[yellow]No valid result files found[/yellow]")
        return

    selected_file = questionary.select(
        "Select a result file to view:",
        choices=file_choices + [questionary.Choice("🔙 Back", None)]
    ).ask()

    if selected_file:
        _show_file_details(selected_file)


def _view_result_details():
    """View details of a specific result file"""
    files = _get_result_files()

    if not files:
        console.print("[yellow]No result files found[/yellow]")
        return

    # Simple file selection
    file_choices = [
        questionary.Choice(file_path.name, file_path)
        for file_path in files
    ]

    selected_file = questionary.select(
        "Select a file to view details:",
        choices=file_choices + [questionary.Choice("🔙 Back", None)]
    ).ask()

    if selected_file:
        _show_file_details(selected_file)


def _show_file_details(file_path: Path):
    """Show detailed information about a result file"""
    try:
        df = pd.read_csv(file_path)

        console.print(Panel(
            f"[bold]File:[/bold] {file_path.name}\n"
            f"[bold]Records:[/bold] {len(df):,}\n"
            f"[bold]Columns:[/bold] {len(df.columns)}\n"
            f"[bold]Size:[/bold] {file_path.stat().st_size / 1024:.1f} KB\n"
            f"[bold]Modified:[/bold] {datetime.fromtimestamp(file_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')}",
            title="File Details",
            border_style="blue"
        ))

        # Show column info
        console.print("\n[bold cyan]Column Information:[/bold cyan]")
        table = Table(show_header=True, header_style="bold blue")
        table.add_column("Column", style="cyan")
        table.add_column("Non-null", style="green")
        table.add_column("Data Type", style="yellow")
        table.add_column("Sample Values", style="dim")

        for col in df.columns:
            non_null = df[col].notna().sum()
            dtype = str(df[col].dtype)

            # Get sample values (non-null)
            sample_values = df[col].dropna().head(3).astype(str).tolist()
            sample_text = ", ".join(sample_values[:2])
            if len(sample_values) > 2:
                sample_text += "..."

            table.add_row(
                col,
                f"{non_null}/{len(df)}",
                dtype,
                sample_text
            )

        console.print(table)

        # Show sample data
        if questionary.confirm("Would you like to see sample data?").ask():
            console.print("\n[bold cyan]Sample Data (first 5 rows):[/bold cyan]")
            console.print(df.head().to_string(index=False, max_cols=10))

    except Exception as e:
        console.print(f"[red]Error reading file: {e}[/red]")


def _compare_results():
    """Compare multiple result files"""
    files = _get_result_files()

    if len(files) < 2:
        console.print("[yellow]Need at least 2 result files to compare[/yellow]")
        return

    console.print("[blue]Select files to compare (select 2-3 files):[/blue]")

    file_choices = [
        questionary.Choice(f"{file_path.name}", file_path)
        for file_path in files
    ]

    selected_files = questionary.checkbox(
        "Select files to compare:",
        choices=file_choices
    ).ask()

    if not selected_files or len(selected_files) < 2:
        console.print("[yellow]Please select at least 2 files to compare[/yellow]")
        return

    # Compare the files
    console.print(f"\n[bold cyan]Comparison of {len(selected_files)} files:[/bold cyan]")

    comparison_table = Table(show_header=True, header_style="bold blue")
    comparison_table.add_column("Metric", style="cyan")

    for file_path in selected_files:
        comparison_table.add_column(file_path.name, style="white")

    # Compare basic metrics
    metrics = []
    for file_path in selected_files:
        try:
            df = pd.read_csv(file_path)
            stat = file_path.stat()

            metrics.append({
                'records': len(df),
                'columns': len(df.columns),
                'size_kb': stat.st_size / 1024,
                'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d')
            })
        except:
            metrics.append({
                'records': 'Error',
                'columns': 'Error',
                'size_kb': 'Error',
                'modified': 'Error'
            })

    # Add rows to comparison table
    comparison_table.add_row("Records", *[str(m['records']) for m in metrics])
    comparison_table.add_row("Columns", *[str(m['columns']) for m in metrics])
    comparison_table.add_row("Size (KB)", *[f"{m['size_kb']:.1f}" if isinstance(m['size_kb'], float) else str(m['size_kb']) for m in metrics])
    comparison_table.add_row("Modified", *[str(m['modified']) for m in metrics])

    console.print(comparison_table)


def _export_results():
    """Export or convert result files"""
    files = _get_result_files()

    if not files:
        console.print("[yellow]No result files found[/yellow]")
        return

    file_choices = [
        questionary.Choice(file_path.name, file_path)
        for file_path in files
    ]

    selected_file = questionary.select(
        "Select file to export/convert:",
        choices=file_choices + [questionary.Choice("🔙 Back", None)]
    ).ask()

    if not selected_file:
        return

    export_choice = questionary.select(
        "Export options:",
        choices=[
            questionary.Choice("📊 Convert to Excel (.xlsx)", "excel"),
            questionary.Choice("📋 Convert to JSON", "json"),
            questionary.Choice("📄 Export summary report", "summary"),
            questionary.Choice("🔙 Back", None)
        ]
    ).ask()

    if not export_choice:
        return

    try:
        df = pd.read_csv(selected_file)
        base_name = selected_file.stem

        if export_choice == "excel":
            output_path = selected_file.parent / f"{base_name}.xlsx"
            df.to_excel(output_path, index=False)
            console.print(f"[green]✓ Exported to Excel: {output_path}[/green]")

        elif export_choice == "json":
            output_path = selected_file.parent / f"{base_name}.json"
            df.to_json(output_path, orient='records', indent=2)
            console.print(f"[green]✓ Exported to JSON: {output_path}[/green]")

        elif export_choice == "summary":
            output_path = selected_file.parent / f"{base_name}_summary.txt"
            with open(output_path, 'w') as f:
                f.write(f"Summary Report for {selected_file.name}\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Total Records: {len(df):,}\n")
                f.write(f"Columns: {len(df.columns)}\n")
                f.write(f"File Size: {selected_file.stat().st_size / 1024:.1f} KB\n\n")

                f.write("Column Information:\n")
                f.write("-" * 20 + "\n")
                for col in df.columns:
                    non_null = df[col].notna().sum()
                    f.write(f"{col}: {non_null}/{len(df)} non-null values\n")

                f.write(f"\nGenerated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            console.print(f"[green]✓ Summary report saved: {output_path}[/green]")

    except Exception as e:
        console.print(f"[red]Error exporting file: {e}[/red]")


def _interactive_clean():
    """Interactive cleanup of old files"""
    files = _get_result_files()

    if not files:
        console.print("[yellow]No result files found[/yellow]")
        return

    console.print(Panel(
        "🧹 Interactive File Cleanup\n\n"
        "Select files to delete or clean up old files automatically.",
        title="Cleanup Options",
        border_style="red"
    ))

    cleanup_choice = questionary.select(
        "Choose cleanup method:",
        choices=[
            questionary.Choice("🗓️ Delete files older than 30 days", "auto"),
            questionary.Choice("📋 Select specific files to delete", "manual"),
            questionary.Choice("🔙 Back", None)
        ]
    ).ask()

    if cleanup_choice == "auto":
        # Auto cleanup (existing logic)
        import time
        current_time = time.time()
        old_files = []

        for file_path in files:
            if current_time - file_path.stat().st_mtime > 30 * 24 * 3600:  # 30 days
                old_files.append(file_path)

        if not old_files:
            console.print("[green]No files older than 30 days found[/green]")
            return

        console.print(f"[yellow]Found {len(old_files)} files older than 30 days:[/yellow]")
        for file_path in old_files:
            console.print(f"  • {file_path.name}")

        if questionary.confirm("Delete these files?").ask():
            for file_path in old_files:
                file_path.unlink()
            console.print(f"[green]✓ Deleted {len(old_files)} old files[/green]")

    elif cleanup_choice == "manual":
        # Manual file selection
        file_choices = [
            questionary.Choice(
                f"{file_path.name} ({file_path.stat().st_size / 1024:.1f} KB)",
                file_path
            )
            for file_path in files
        ]

        files_to_delete = questionary.checkbox(
            "Select files to delete:",
            choices=file_choices
        ).ask()

        if files_to_delete:
            console.print(f"\n[red]You are about to delete {len(files_to_delete)} files:[/red]")
            for file_path in files_to_delete:
                console.print(f"  • {file_path.name}")

            if questionary.confirm("Are you sure you want to delete these files?").ask():
                for file_path in files_to_delete:
                    file_path.unlink()
                console.print(f"[green]✓ Deleted {len(files_to_delete)} files[/green]")
            else:
                console.print("[yellow]Deletion cancelled[/yellow]")


@results_app.callback()
def results_callback():
    """Results management commands"""
    pass
