"""
Data export utilities for business data.
"""
import pandas as pd
import json
import csv
import logging
from typing import List, Dict, Any
from datetime import datetime
import os
from config import OUTPUT_DIR, OUTPUT_FILENAME

logger = logging.getLogger(__name__)


class DataExporter:
    """Handles exporting business data to various formats."""
    
    def __init__(self, output_dir: str = OUTPUT_DIR):
        """Initialize the data exporter.
        
        Args:
            output_dir: Directory to save output files
        """
        self.output_dir = output_dir
        self._ensure_output_dir()
        
    def _ensure_output_dir(self):
        """Ensure output directory exists."""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
    def export_to_csv(self, 
                     businesses: List[Dict[str, Any]], 
                     filename: str = None) -> str:
        """Export business data to CSV format.
        
        Args:
            businesses: List of business data dictionaries
            filename: Output filename (optional)
            
        Returns:
            Path to the exported CSV file
        """
        if not businesses:
            logger.warning("No businesses to export")
            return None
            
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"businesses_{timestamp}.csv"
            
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            # Convert to DataFrame for easier handling
            df = pd.DataFrame(businesses)
            
            # Standardize columns
            df = self._standardize_dataframe(df)
            
            # Export to CSV
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            logger.info(f"Exported {len(businesses)} businesses to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting to CSV: {str(e)}")
            return None
    
    def export_to_json(self, 
                      businesses: List[Dict[str, Any]], 
                      filename: str = None) -> str:
        """Export business data to JSON format.
        
        Args:
            businesses: List of business data dictionaries
            filename: Output filename (optional)
            
        Returns:
            Path to the exported JSON file
        """
        if not businesses:
            logger.warning("No businesses to export")
            return None
            
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"businesses_{timestamp}.json"
            
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            # Add export metadata
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'total_businesses': len(businesses),
                'businesses': businesses
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Exported {len(businesses)} businesses to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting to JSON: {str(e)}")
            return None
    
    def export_summary_report(self, 
                            businesses: List[Dict[str, Any]], 
                            filename: str = None) -> str:
        """Export a summary report of the collected data.
        
        Args:
            businesses: List of business data dictionaries
            filename: Output filename (optional)
            
        Returns:
            Path to the exported report file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"business_summary_{timestamp}.txt"
            
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("BUSINESS DATA COLLECTION SUMMARY REPORT\n")
                f.write("=" * 50 + "\n\n")
                
                # Basic statistics
                f.write(f"Total Businesses Collected: {len(businesses)}\n")
                f.write(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # Source breakdown
                sources = {}
                for business in businesses:
                    source = business.get('source', 'unknown')
                    sources[source] = sources.get(source, 0) + 1
                
                f.write("BREAKDOWN BY SOURCE:\n")
                f.write("-" * 20 + "\n")
                for source, count in sorted(sources.items()):
                    f.write(f"{source}: {count} businesses\n")
                f.write("\n")
                
                # Business type breakdown
                types = {}
                for business in businesses:
                    business_types = business.get('types', [])
                    if isinstance(business_types, list):
                        for btype in business_types:
                            types[btype] = types.get(btype, 0) + 1
                    elif business_types:
                        types[business_types] = types.get(business_types, 0) + 1
                
                f.write("TOP BUSINESS TYPES:\n")
                f.write("-" * 20 + "\n")
                sorted_types = sorted(types.items(), key=lambda x: x[1], reverse=True)
                for btype, count in sorted_types[:20]:  # Top 20
                    f.write(f"{btype}: {count} businesses\n")
                f.write("\n")
                
                # Data completeness
                f.write("DATA COMPLETENESS:\n")
                f.write("-" * 20 + "\n")
                
                fields = ['name', 'address', 'phone', 'website']
                for field in fields:
                    count = sum(1 for b in businesses if b.get(field, '').strip())
                    percentage = (count / len(businesses)) * 100 if businesses else 0
                    f.write(f"{field}: {count}/{len(businesses)} ({percentage:.1f}%)\n")
                f.write("\n")
                
                # Sample businesses
                f.write("SAMPLE BUSINESSES:\n")
                f.write("-" * 20 + "\n")
                for i, business in enumerate(businesses[:10]):  # First 10
                    f.write(f"{i+1}. {business.get('name', 'Unknown')}\n")
                    if business.get('address'):
                        f.write(f"   Address: {business['address']}\n")
                    if business.get('phone'):
                        f.write(f"   Phone: {business['phone']}\n")
                    if business.get('website'):
                        f.write(f"   Website: {business['website']}\n")
                    f.write(f"   Source: {business.get('source', 'unknown')}\n\n")
                
            logger.info(f"Exported summary report to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting summary report: {str(e)}")
            return None
    
    def _standardize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize DataFrame columns and data types.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Standardized DataFrame
        """
        # Define standard columns in preferred order
        standard_columns = [
            'name', 'address', 'phone', 'website', 'primary_type', 'types',
            'business_status', 'latitude', 'longitude', 'rating', 
            'user_rating_count', 'price_level', 'source'
        ]
        
        # Add missing columns
        for col in standard_columns:
            if col not in df.columns:
                df[col] = ''
        
        # Handle list columns (convert to string)
        list_columns = ['types']
        for col in list_columns:
            if col in df.columns:
                df[col] = df[col].apply(lambda x: ', '.join(x) if isinstance(x, list) else str(x))
        
        # Reorder columns
        available_columns = [col for col in standard_columns if col in df.columns]
        other_columns = [col for col in df.columns if col not in standard_columns]
        df = df[available_columns + other_columns]
        
        # Clean up data
        df = df.fillna('')
        
        return df
