"""
Google Places API collector for business data.
"""
import requests
import time
import logging
from typing import List, Dict, Any, Optional
from config import (
    GOOGLE_PLACES_API_KEY,
    PITTSBURGH_LAT,
    PITTSBURGH_LNG,
    SEARCH_RADIUS_METERS,
    <PERSON><PERSON><PERSON><PERSON>_PLACES_DELAY,
    GOO<PERSON>LE_PLACES_TYPES,
    CORPORATE_BLACKLIST
)

logger = logging.getLogger(__name__)


class GooglePlacesCollector:
    """Collector for business data from Google Places API."""
    
    def __init__(self, api_key: str = GOOGLE_PLACES_API_KEY):
        """Initialize the Google Places collector.

        Args:
            api_key: Google Places API key
        """
        self.api_key = api_key
        self.base_url = "https://places.googleapis.com/v1/places:searchNearby"
        self.legacy_url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
        self.details_url = "https://maps.googleapis.com/maps/api/place/details/json"
        self.session = requests.Session()
        self.use_legacy = False  # Flag to switch to legacy API if new API fails

        # Prepare corporate blacklist for early filtering
        self.corporate_blacklist = [name.lower().strip() for name in CORPORATE_BLACKLIST]
        
    def collect_businesses(self,
                          lat: float = PITTSBURGH_LAT,
                          lng: float = PITTSBURGH_LNG,
                          radius: int = SEARCH_RADIUS_METERS,
                          max_results: int = 15,  # Reduced from 20 to save API calls
                          business_types: Optional[List[str]] = None,
                          text_query: Optional[str] = None) -> List[Dict[str, Any]]:
        """Collect business data from Google Places API.

        Args:
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters
            max_results: Maximum results per request
            business_types: Specific business types to search for (if None, uses default types)
            text_query: Text query to search for (used when business_types is empty)

        Returns:
            List of business data dictionaries
        """
        businesses = []

        # Handle text query search when no business types are provided
        if text_query and (not business_types or len(business_types) == 0):
            logger.info(f"Using text search for: {text_query}")
            try:
                text_businesses = self._search_by_text(text_query, lat, lng, radius, max_results)
                businesses.extend(text_businesses)
                logger.info(f"Found {len(text_businesses)} businesses via text search")
                return businesses
            except Exception as e:
                logger.error(f"Error in text search for '{text_query}': {str(e)}")
                return []

        # Use provided business types or default ones
        types_to_search = business_types if business_types is not None else GOOGLE_PLACES_TYPES

        # Store current search types for filtering
        self._current_search_types = business_types

        # Search for different business types
        for business_type in types_to_search:
            logger.info(f"Searching for {business_type} businesses...")

            try:
                if self.use_legacy:
                    type_businesses = self._search_by_type_legacy(
                        business_type, lat, lng, radius, max_results
                    )
                else:
                    type_businesses = self._search_by_type(
                        business_type, lat, lng, radius, max_results
                    )

                    # If new API fails with 403, switch to legacy
                    if not type_businesses and not self.use_legacy:
                        logger.info(f"Switching to legacy API for {business_type}")
                        self.use_legacy = True
                        type_businesses = self._search_by_type_legacy(
                            business_type, lat, lng, radius, max_results
                        )

                businesses.extend(type_businesses)
                logger.info(f"Found {len(type_businesses)} {business_type} businesses")

                # Rate limiting
                time.sleep(GOOGLE_PLACES_DELAY)

            except Exception as e:
                logger.error(f"Error searching for {business_type}: {str(e)}")
                continue
                
        logger.info(f"Collected {len(businesses)} businesses from Google Places API")
        return businesses

    def _is_corporate_chain(self, business_name: str) -> bool:
        """Check if a business name matches a corporate chain.

        Args:
            business_name: Name of the business to check

        Returns:
            True if the business appears to be a corporate chain
        """
        if not business_name:
            return False

        # Normalize name: lowercase, remove apostrophes, extra spaces
        normalized_name = business_name.lower().strip().replace("'", "").replace("'", "")

        # Direct exact match only (most conservative approach)
        for corporate_name in self.corporate_blacklist:
            clean_corporate = corporate_name.replace("'", "").replace("'", "")

            # Exact match
            if normalized_name == clean_corporate:
                return True

            # Match with common suffixes (but be very specific)
            common_suffixes = ['restaurant', 'cafe', 'coffee', 'shop', 'store', 'inc', 'llc', 'sandwich shop', 'sandwiches']
            for suffix in common_suffixes:
                if normalized_name == f"{clean_corporate} {suffix}":
                    return True
                if normalized_name == f"{clean_corporate}s":  # plural form
                    return True

            # Only match if corporate name is the FIRST word and followed by location/number
            words = normalized_name.split()
            if len(words) >= 2 and words[0] == clean_corporate:
                # Check if second word looks like a location identifier
                second_word = words[1]
                if (second_word.isdigit() or  # Starbucks 123
                    len(second_word) <= 3 or  # Starbucks NYC
                    second_word in ['st', 'ave', 'rd', 'blvd', 'dr', 'ln', 'way', 'plaza', 'mall', 'center']):
                    return True

        return False

    def _get_business_types(self) -> List[str]:
        """Get current business types being used"""
        return GOOGLE_PLACES_TYPES.copy()

    def _set_business_types(self, types: List[str]):
        """Temporarily set business types for collection"""
        # This is a bit of a hack, but we'll modify the global for this session
        global GOOGLE_PLACES_TYPES
        GOOGLE_PLACES_TYPES[:] = types

    def _search_by_text(self,
                       text_query: str,
                       lat: float,
                       lng: float,
                       radius: int,
                       max_results: int) -> List[Dict[str, Any]]:
        """Search for businesses using text query.

        Args:
            text_query: Text to search for (e.g., "financial advisor")
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters
            max_results: Maximum results per request

        Returns:
            List of business data dictionaries
        """
        # Use Google Places Text Search API
        text_search_url = "https://places.googleapis.com/v1/places:searchText"

        headers = {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': self.api_key,
            'X-Goog-FieldMask': (
                'places.displayName,'
                'places.formattedAddress,'
                'places.internationalPhoneNumber,'
                'places.nationalPhoneNumber,'
                'places.websiteUri,'
                'places.types,'
                'places.primaryType,'
                'places.businessStatus,'
                'places.location,'
                'places.rating,'
                'places.userRatingCount,'
                'places.priceLevel'
            )
        }

        payload = {
            "textQuery": text_query,
            "maxResultCount": max_results,
            "locationBias": {
                "circle": {
                    "center": {
                        "latitude": lat,
                        "longitude": lng
                    },
                    "radius": radius
                }
            },
            "rankPreference": "RELEVANCE"
        }

        try:
            response = self.session.post(
                text_search_url,
                json=payload,
                headers=headers,
                timeout=30
            )

            logger.debug(f"Text search response status: {response.status_code}")

            if response.status_code == 403:
                logger.error(f"API key forbidden (403) for text search '{text_query}'. Check API key and billing.")
                return []
            elif response.status_code == 400:
                logger.error(f"Bad request (400) for text search '{text_query}'. Response: {response.text}")
                return []

            response.raise_for_status()

            data = response.json()
            places = data.get('places', [])

            businesses = []
            for place in places:
                business = self._parse_place_data(place)
                if business:
                    # Early corporate chain filtering to save API calls
                    if self._is_corporate_chain(business['name']):
                        logger.debug(f"Filtered out corporate chain: {business['name']}")
                        continue
                    businesses.append(business)

            return businesses

        except Exception as e:
            logger.error(f"Error in text search for '{text_query}': {str(e)}")
            return []

    def _search_by_type(self,
                       business_type: str,
                       lat: float,
                       lng: float,
                       radius: int,
                       max_results: int) -> List[Dict[str, Any]]:
        """Search for businesses of a specific type.
        
        Args:
            business_type: Type of business to search for
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters
            max_results: Maximum results per request
            
        Returns:
            List of business data dictionaries
        """
        headers = {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': self.api_key,
            'X-Goog-FieldMask': (
                'places.displayName,'
                'places.formattedAddress,'
                'places.internationalPhoneNumber,'
                'places.nationalPhoneNumber,'
                'places.websiteUri,'
                'places.types,'
                'places.primaryType,'
                'places.businessStatus,'
                'places.location,'
                'places.rating,'
                'places.userRatingCount,'
                'places.priceLevel'
            )
        }
        
        payload = {
            "includedTypes": [business_type],
            "maxResultCount": max_results,
            "locationRestriction": {
                "circle": {
                    "center": {
                        "latitude": lat,
                        "longitude": lng
                    },
                    "radius": radius
                }
            },
            "rankPreference": "POPULARITY"
        }
        
        try:
            response = self.session.post(
                self.base_url,
                json=payload,
                headers=headers,
                timeout=30
            )

            # Log response details for debugging
            logger.debug(f"Response status: {response.status_code}")
            logger.debug(f"Response headers: {dict(response.headers)}")

            if response.status_code == 403:
                logger.error(f"API key forbidden (403) for {business_type}. Check API key and billing.")
                return []
            elif response.status_code == 400:
                logger.error(f"Bad request (400) for {business_type}. Response: {response.text}")
                return []

            response.raise_for_status()

            data = response.json()
            places = data.get('places', [])

            businesses = []
            for place in places:
                business = self._parse_place_data(place)
                if business:
                    # Early corporate chain filtering to save API calls
                    if self._is_corporate_chain(business['name']):
                        logger.debug(f"Filtered out corporate chain: {business['name']}")
                        continue

                    # Filter by primary type if we're searching for specific types
                    if business_types and len(business_types) == 1:
                        target_type = business_types[0]
                        primary_type = business.get('primary_type', '')
                        business_name = business.get('name', '').lower()

                        # Special filtering for financial advisors when searching accounting type
                        if target_type == 'accounting':
                            # Look for financial advisor keywords in business name
                            financial_keywords = ['financial', 'advisor', 'investment', 'wealth', 'planning', 'portfolio', 'retirement']
                            if any(keyword in business_name for keyword in financial_keywords):
                                businesses.append(business)
                                logger.debug(f"Included {business['name']} - contains financial keywords")
                            elif primary_type == target_type:
                                businesses.append(business)
                            else:
                                logger.debug(f"Filtered out {business['name']} - no financial keywords and primary type '{primary_type}' != target '{target_type}'")
                        else:
                            # Regular filtering for other types
                            if primary_type == target_type:
                                businesses.append(business)
                            else:
                                logger.debug(f"Filtered out {business['name']} - primary type '{primary_type}' != target '{target_type}'")
                    else:
                        businesses.append(business)

            logger.info(f"Successfully collected {len(businesses)} businesses for {business_type}")
            return businesses

        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for {business_type}: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response content: {e.response.text}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error for {business_type}: {str(e)}")
            return []
    
    def _parse_place_data(self, place: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse place data from Google Places API response.
        
        Args:
            place: Place data from API response
            
        Returns:
            Parsed business data dictionary or None if invalid
        """
        try:
            # Extract basic information
            display_name = place.get('displayName', {})
            name = display_name.get('text', '').strip()
            
            if not name:
                return None
                
            # Extract contact information
            address = place.get('formattedAddress', '').strip()
            phone = (place.get('internationalPhoneNumber') or 
                    place.get('nationalPhoneNumber', '')).strip()
            website = place.get('websiteUri', '').strip()
            
            # Extract business details
            types = place.get('types', [])
            primary_type = place.get('primaryType', '')
            business_status = place.get('businessStatus', '')
            
            # Extract location
            location = place.get('location', {})
            latitude = location.get('latitude')
            longitude = location.get('longitude')
            
            # Extract ratings
            rating = place.get('rating')
            user_rating_count = place.get('userRatingCount')
            price_level = place.get('priceLevel')
            
            business_data = {
                'name': name,
                'address': address,
                'phone': phone,
                'website': website,
                'facebook': '',  # Not available from Google Places API
                'instagram': '',  # Not available from Google Places API
                'twitter': '',  # Not available from Google Places API
                'linkedin': '',  # Not available from Google Places API
                'yelp': '',  # Not available from Google Places API
                'types': types,
                'primary_type': primary_type,
                'business_status': business_status,
                'latitude': latitude,
                'longitude': longitude,
                'rating': rating,
                'user_rating_count': user_rating_count,
                'price_level': price_level,
                'source': 'google_places'
            }
            
            return business_data

        except Exception as e:
            logger.error(f"Error parsing place data: {str(e)}")
            return None

    def _search_by_type_legacy(self,
                              business_type: str,
                              lat: float,
                              lng: float,
                              radius: int,
                              max_results: int) -> List[Dict[str, Any]]:
        """Search for businesses using legacy Google Places API.

        Args:
            business_type: Type of business to search for
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters
            max_results: Maximum results per request

        Returns:
            List of business data dictionaries
        """
        params = {
            'location': f"{lat},{lng}",
            'radius': radius,
            'type': business_type,
            'key': self.api_key
        }

        try:
            response = self.session.get(
                self.legacy_url,
                params=params,
                timeout=30
            )

            logger.debug(f"Legacy API response status: {response.status_code}")

            if response.status_code == 403:
                logger.error(f"Legacy API key forbidden (403) for {business_type}")
                return []

            response.raise_for_status()

            data = response.json()
            results = data.get('results', [])

            businesses = []
            for place in results[:max_results]:  # Limit results
                business = self._parse_legacy_place_data(place)
                if business:
                    # Early corporate chain filtering to save API calls
                    if self._is_corporate_chain(business['name']):
                        logger.debug(f"Filtered out corporate chain: {business['name']}")
                        continue

                    # Filter by primary type if we're searching for specific types
                    if hasattr(self, '_current_search_types') and self._current_search_types and len(self._current_search_types) == 1:
                        target_type = self._current_search_types[0]
                        primary_type = business.get('primary_type', '')
                        if primary_type == target_type:
                            # Only enhance with place details if it passes all filters (saves API calls)
                            enhanced_business = self._enhance_with_place_details(business, place.get('place_id'))
                            businesses.append(enhanced_business)
                        else:
                            logger.debug(f"Filtered out {business['name']} - primary type '{primary_type}' != target '{target_type}'")
                    else:
                        # Only enhance with place details if it passes all filters (saves API calls)
                        enhanced_business = self._enhance_with_place_details(business, place.get('place_id'))
                        businesses.append(enhanced_business)

            logger.info(f"Legacy API collected {len(businesses)} businesses for {business_type}")
            return businesses

        except requests.exceptions.RequestException as e:
            logger.error(f"Legacy API request error for {business_type}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Legacy API unexpected error for {business_type}: {str(e)}")
            return []

    def _parse_legacy_place_data(self, place: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse place data from legacy Google Places API response.

        Args:
            place: Place data from legacy API response

        Returns:
            Parsed business data dictionary or None if invalid
        """
        try:
            name = place.get('name', '').strip()
            if not name:
                return None

            # Extract location
            geometry = place.get('geometry', {})
            location = geometry.get('location', {})
            latitude = location.get('lat')
            longitude = location.get('lng')

            # Extract other fields
            address = place.get('vicinity', '').strip()
            types = place.get('types', [])
            business_status = 'OPERATIONAL' if place.get('business_status') == 'OPERATIONAL' else 'UNKNOWN'
            rating = place.get('rating')
            user_rating_count = place.get('user_ratings_total')
            price_level = place.get('price_level')

            business_data = {
                'name': name,
                'address': address,
                'phone': '',  # Not available in legacy nearby search
                'website': '',  # Will be enhanced with place details
                'facebook': '',  # Not available from Google Places API
                'instagram': '',  # Not available from Google Places API
                'twitter': '',  # Not available from Google Places API
                'linkedin': '',  # Not available from Google Places API
                'yelp': '',  # Not available from Google Places API
                'types': types,
                'primary_type': types[0] if types else '',
                'business_status': business_status,
                'latitude': latitude,
                'longitude': longitude,
                'rating': rating,
                'user_rating_count': user_rating_count,
                'price_level': price_level,
                'source': 'google_places_legacy'
            }

            return business_data

        except Exception as e:
            logger.error(f"Error parsing legacy place data: {str(e)}")
            return None

    def _enhance_with_place_details(self, business: Dict[str, Any], place_id: Optional[str]) -> Dict[str, Any]:
        """Enhance business data with place details including website and social media.

        Args:
            business: Basic business data
            place_id: Google Places place_id for detailed lookup

        Returns:
            Enhanced business data with website and social media info
        """
        if not place_id:
            return business

        # Skip place details API call if business already has a website
        if business.get('website'):
            logger.debug(f"Skipping place details for {business['name']} - already has website")
            return business

        try:
            # Request place details with website and social media fields
            params = {
                'place_id': place_id,
                'fields': 'website,url,editorial_summary,reviews,photos',
                'key': self.api_key
            }

            response = self.session.get(
                self.details_url,
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                result = data.get('result', {})

                # Extract website if available
                website = result.get('website', '').strip()
                if website:
                    business['website'] = website

                # Extract Google Maps URL
                google_url = result.get('url', '').strip()
                if google_url:
                    business['google_maps_url'] = google_url

                # Extract additional info from reviews/photos for social media
                reviews = result.get('reviews', [])
                if reviews:
                    business['has_reviews'] = True
                    business['review_count'] = len(reviews)

                photos = result.get('photos', [])
                if photos:
                    business['has_photos'] = True
                    business['photo_count'] = len(photos)

            else:
                logger.debug(f"Place details request failed for {place_id}: {response.status_code}")

        except Exception as e:
            logger.debug(f"Error getting place details for {place_id}: {str(e)}")

        return business
