"""
Apify Facebook Page Scraper Integration for Business Data Pipeline.
Extracts emails and additional contact information from Facebook business pages.
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import requests
import pandas as pd

try:
    from apify_client import ApifyClient
    APIFY_CLIENT_AVAILABLE = True
except ImportError:
    APIFY_CLIENT_AVAILABLE = False

logger = logging.getLogger(__name__)


class ApifyFacebookScraper:
    """Integrates Apify Facebook Page Scraper into the business data pipeline."""

    def __init__(self, apify_token: str, actor_id: str = "Catqz8yCm9MEuNd8x"):
        """Initialize the Apify Facebook scraper.

        Args:
            apify_token: Your Apify API token
            actor_id: Apify actor ID for Facebook page scraper
        """
        self.apify_token = apify_token
        self.actor_id = actor_id

        # Use official Apify client if available, fallback to requests
        if APIFY_CLIENT_AVAILABLE:
            self.client = ApifyClient(apify_token)
            self.use_client = True
            logger.info("Using official Apify client")
        else:
            self.use_client = False
            self.base_url = "https://api.apify.com/v2"
            self.session = requests.Session()
            self.session.headers.update({
                'Authorization': f'Bearer {apify_token}',
                'Content-Type': 'application/json'
            })
            logger.info("Using requests-based Apify integration")
    
    def extract_facebook_emails(self, csv_file_path: str, 
                               facebook_column: str = 'facebook',
                               output_file: Optional[str] = None) -> pd.DataFrame:
        """Extract emails from Facebook pages in CSV data.
        
        Args:
            csv_file_path: Path to CSV file with business data
            facebook_column: Column name containing Facebook URLs
            output_file: Optional output file path for results
            
        Returns:
            DataFrame with updated email information
        """
        logger.info(f"Loading business data from {csv_file_path}")
        
        # Load the CSV data
        df = pd.read_csv(csv_file_path)
        
        # Filter rows that have Facebook URLs
        facebook_rows = df[df[facebook_column].notna() & (df[facebook_column] != '')]
        
        if facebook_rows.empty:
            logger.warning("No Facebook URLs found in the data")
            return df
        
        logger.info(f"Found {len(facebook_rows)} businesses with Facebook pages")
        
        # Extract Facebook URLs
        facebook_urls = facebook_rows[facebook_column].tolist()
        
        # Process Facebook URLs in batches
        batch_size = 10  # Adjust based on your Apify plan limits
        results = []
        
        for i in range(0, len(facebook_urls), batch_size):
            batch_urls = facebook_urls[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: {len(batch_urls)} URLs")
            
            batch_results = self._scrape_facebook_batch(batch_urls)
            results.extend(batch_results)
            
            # Rate limiting - adjust based on your Apify plan
            if i + batch_size < len(facebook_urls):
                time.sleep(5)  # 5 second delay between batches
        
        # Update the DataFrame with results
        updated_df = self._update_dataframe_with_results(df, facebook_rows, results)
        
        # Save results if output file specified
        if output_file:
            updated_df.to_csv(output_file, index=False)
            logger.info(f"Results saved to {output_file}")
        
        return updated_df
    
    def _scrape_facebook_batch(self, facebook_urls: List[str]) -> List[Dict[str, Any]]:
        """Scrape a batch of Facebook URLs using Apify.

        Args:
            facebook_urls: List of Facebook URLs to scrape

        Returns:
            List of scraping results
        """
        if self.use_client:
            return self._scrape_with_client(facebook_urls)
        else:
            return self._scrape_with_requests(facebook_urls)

    def _scrape_with_client(self, facebook_urls: List[str]) -> List[Dict[str, Any]]:
        """Scrape using official Apify client."""
        # Prepare input for Apify actor using the correct format
        run_input = {
            "urls": facebook_urls  # Simple list of URLs as shown in your example
        }

        try:
            # Run the actor and wait for completion
            logger.info(f"Starting Apify actor run for {len(facebook_urls)} URLs")
            run = self.client.actor(self.actor_id).call(run_input=run_input)

            # Get results from the dataset
            results = []
            for item in self.client.dataset(run["defaultDatasetId"]).iterate_items():
                results.append(item)

            logger.info(f"Apify run completed. Retrieved {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"Error in Apify client scraping: {str(e)}")
            return []

    def _scrape_with_requests(self, facebook_urls: List[str]) -> List[Dict[str, Any]]:
        """Scrape using requests-based approach (fallback)."""
        # Prepare input for Apify actor using the correct format
        run_input = {
            "urls": facebook_urls  # Simple list of URLs
        }

        try:
            # Start the actor run
            run_response = self._start_actor_run(run_input)

            if not run_response:
                logger.error("Failed to start Apify actor run")
                return []

            run_id = run_response.get('data', {}).get('id')
            if not run_id:
                logger.error("No run ID received from Apify")
                return []

            # Wait for completion and get results
            results = self._wait_for_completion_and_get_results(run_id)
            return results

        except Exception as e:
            logger.error(f"Error in Facebook batch scraping: {str(e)}")
            return []
    
    def _start_actor_run(self, actor_input: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Start an Apify actor run.
        
        Args:
            actor_input: Input configuration for the actor
            
        Returns:
            API response or None if failed
        """
        url = f"{self.base_url}/acts/{self.actor_id}/runs"
        
        try:
            response = self.session.post(url, json=actor_input, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to start actor run: {str(e)}")
            return None
    
    def _wait_for_completion_and_get_results(self, run_id: str, 
                                           max_wait_time: int = 300) -> List[Dict[str, Any]]:
        """Wait for actor run completion and retrieve results.
        
        Args:
            run_id: Apify run ID
            max_wait_time: Maximum time to wait in seconds
            
        Returns:
            List of scraping results
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            # Check run status
            status_url = f"{self.base_url}/actor-runs/{run_id}"
            
            try:
                status_response = self.session.get(status_url, timeout=10)
                status_response.raise_for_status()
                status_data = status_response.json()
                
                run_status = status_data.get('data', {}).get('status')
                
                if run_status == 'SUCCEEDED':
                    logger.info(f"Actor run {run_id} completed successfully")
                    return self._get_run_results(run_id)
                elif run_status in ['FAILED', 'ABORTED', 'TIMED-OUT']:
                    logger.error(f"Actor run {run_id} failed with status: {run_status}")
                    return []
                else:
                    # Still running, wait and check again
                    time.sleep(10)
                    
            except Exception as e:
                logger.error(f"Error checking run status: {str(e)}")
                time.sleep(10)
        
        logger.error(f"Actor run {run_id} timed out after {max_wait_time} seconds")
        return []
    
    def _get_run_results(self, run_id: str) -> List[Dict[str, Any]]:
        """Retrieve results from completed actor run.
        
        Args:
            run_id: Apify run ID
            
        Returns:
            List of scraping results
        """
        results_url = f"{self.base_url}/actor-runs/{run_id}/dataset/items"
        
        try:
            response = self.session.get(results_url, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get run results: {str(e)}")
            return []
    
    def _update_dataframe_with_results(self, df: pd.DataFrame, 
                                     facebook_rows: pd.DataFrame,
                                     results: List[Dict[str, Any]]) -> pd.DataFrame:
        """Update DataFrame with Facebook scraping results.
        
        Args:
            df: Original DataFrame
            facebook_rows: Rows with Facebook URLs
            results: Scraping results from Apify
            
        Returns:
            Updated DataFrame
        """
        # Create a copy of the original DataFrame
        updated_df = df.copy()
        
        # Add new columns if they don't exist
        if 'facebook_email' not in updated_df.columns:
            updated_df['facebook_email'] = ''
        if 'facebook_email_confidence' not in updated_df.columns:
            updated_df['facebook_email_confidence'] = 0.0
        if 'facebook_scraped_at' not in updated_df.columns:
            updated_df['facebook_scraped_at'] = ''
        
        # Map results back to DataFrame rows
        facebook_urls = facebook_rows['facebook'].tolist()

        # Create a mapping of URLs to results for better matching
        url_to_result = {}
        for result in results:
            # The Facebook scraper returns different fields, check common ones
            result_url = result.get('url', '') or result.get('pageUrl', '') or result.get('link', '')
            if result_url:
                # Normalize URLs for matching
                normalized_url = result_url.lower().strip('/')
                url_to_result[normalized_url] = result

        # Match results to DataFrame rows
        for idx, row in facebook_rows.iterrows():
            facebook_url = row['facebook']
            if not facebook_url:
                continue

            # Try to find matching result
            normalized_fb_url = facebook_url.lower().strip('/')
            result = url_to_result.get(normalized_fb_url)

            # If exact match not found, try partial matching
            if not result:
                for result_url, result_data in url_to_result.items():
                    if normalized_fb_url in result_url or result_url in normalized_fb_url:
                        result = result_data
                        break

            if result:
                # Extract emails from various possible fields in the result
                emails = []

                # Check different possible email fields from Facebook scraper
                if 'emails' in result and result['emails']:
                    emails.extend(result['emails'])
                if 'email' in result and result['email']:
                    emails.append(result['email'])
                if 'contactEmail' in result and result['contactEmail']:
                    emails.append(result['contactEmail'])

                # Also check in text content if available
                if 'text' in result or 'content' in result:
                    content = result.get('text', '') or result.get('content', '')
                    import re
                    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                    found_emails = re.findall(email_pattern, content, re.IGNORECASE)
                    emails.extend(found_emails)

                if emails:
                    # Remove duplicates and filter
                    unique_emails = list(set(emails))
                    best_email = self._select_best_facebook_email(unique_emails)

                    if best_email:
                        updated_df.at[idx, 'facebook_email'] = best_email
                        updated_df.at[idx, 'facebook_email_confidence'] = 0.9  # High confidence from Facebook
                        updated_df.at[idx, 'facebook_scraped_at'] = datetime.now().isoformat()

                        logger.info(f"Found email for {facebook_url}: {best_email}")

        return updated_df

    def _select_best_facebook_email(self, emails: List[str]) -> str:
        """Select the best email from a list of found emails."""
        if not emails:
            return ""

        # Filter out obvious non-business emails
        filtered_emails = []
        for email in emails:
            email_lower = email.lower()
            # Skip common non-business patterns
            if not any(skip in email_lower for skip in ['noreply', 'no-reply', 'donotreply', 'support@facebook']):
                filtered_emails.append(email)

        # Return the first good email, or first email if none filtered
        return filtered_emails[0] if filtered_emails else emails[0]


def extract_facebook_emails_from_csv(csv_file_path: str,
                                   apify_token: str,
                                   facebook_column: str = 'facebook',
                                   output_file: Optional[str] = None) -> pd.DataFrame:
    """Convenience function to extract Facebook emails from CSV.

    Args:
        csv_file_path: Path to CSV file with business data
        apify_token: Your Apify API token
        facebook_column: Column name containing Facebook URLs
        output_file: Optional output file path for results

    Returns:
        DataFrame with updated email information
    """
    scraper = ApifyFacebookScraper(apify_token)
    return scraper.extract_facebook_emails(csv_file_path, facebook_column, output_file)


def extract_facebook_emails_from_csv_with_social_platform(csv_file_path: str,
                                                        apify_token: str,
                                                        output_file: Optional[str] = None) -> pd.DataFrame:
    """Extract Facebook emails from CSV with social_platform/social_url structure.

    This function is specifically designed for the CLI pipeline output format where
    Facebook URLs are stored in 'social_url' column with 'social_platform' = 'facebook'.

    Args:
        csv_file_path: Path to CSV file with business data
        apify_token: Your Apify API token
        output_file: Optional output file path for results

    Returns:
        DataFrame with updated email information
    """
    logger.info(f"Loading business data from {csv_file_path}")

    # Load the CSV data
    df = pd.read_csv(csv_file_path)

    # Filter rows that have Facebook URLs in social_url column where social_platform is 'facebook'
    facebook_rows = df[(df['social_platform'] == 'facebook') &
                       (df['social_url'].notna()) &
                       (df['social_url'] != '')]

    if facebook_rows.empty:
        logger.warning("No Facebook URLs found in the data")
        logger.info("Available social platforms: %s", df['social_platform'].value_counts().to_dict())
        return df

    logger.info(f"Found {len(facebook_rows)} businesses with Facebook pages")

    # Extract Facebook URLs
    facebook_urls = facebook_rows['social_url'].tolist()

    # Initialize the scraper
    scraper = ApifyFacebookScraper(apify_token)

    # Process Facebook URLs in batches
    batch_size = 5  # Smaller batches for better control
    results = []

    for i in range(0, len(facebook_urls), batch_size):
        batch_urls = facebook_urls[i:i + batch_size]
        logger.info(f"Processing batch {i//batch_size + 1}: {len(batch_urls)} URLs")

        batch_results = scraper._scrape_facebook_batch(batch_urls)
        results.extend(batch_results)

        # Rate limiting - adjust based on your Apify plan
        if i + batch_size < len(facebook_urls):
            import time
            time.sleep(10)  # 10 second delay between batches

    # Update the DataFrame with results
    updated_df = _update_dataframe_with_facebook_results_social_platform(df, facebook_rows, results)

    # Save results if output file specified
    if output_file:
        updated_df.to_csv(output_file, index=False)
        logger.info(f"Results saved to {output_file}")

    return updated_df


def _update_dataframe_with_facebook_results_social_platform(df, facebook_rows, results):
    """Update DataFrame with Facebook scraping results for social platform structure."""

    # Create a copy of the original DataFrame
    updated_df = df.copy()

    # Add new columns if they don't exist
    if 'facebook_email' not in updated_df.columns:
        updated_df['facebook_email'] = ''
    if 'facebook_email_confidence' not in updated_df.columns:
        updated_df['facebook_email_confidence'] = 0.0
    if 'facebook_scraped_at' not in updated_df.columns:
        updated_df['facebook_scraped_at'] = ''

    # Create a mapping of URLs to results for better matching
    url_to_result = {}
    for result in results:
        result_url = result.get('url', '')
        if result_url:
            # Normalize URLs for matching
            normalized_url = result_url.lower().strip('/')
            url_to_result[normalized_url] = result

    # Match results to DataFrame rows
    for idx, row in facebook_rows.iterrows():
        facebook_url = row['social_url']
        if not facebook_url:
            continue

        # Try to find matching result
        normalized_fb_url = facebook_url.lower().strip('/')
        result = url_to_result.get(normalized_fb_url)

        # If exact match not found, try partial matching
        if not result:
            for result_url, result_data in url_to_result.items():
                if normalized_fb_url in result_url or result_url in normalized_fb_url:
                    result = result_data
                    break

        if result:
            # Extract email from the result
            email = result.get('email', '')

            if email:
                updated_df.at[idx, 'facebook_email'] = email
                updated_df.at[idx, 'facebook_email_confidence'] = 0.95  # High confidence from Facebook
                updated_df.at[idx, 'facebook_scraped_at'] = datetime.now().isoformat()

                logger.info(f"Found email for {facebook_url}: {email}")

    return updated_df
