"""
Overpass API collector for business data from OpenStreetMap.
"""
import requests
import time
import logging
from typing import List, Dict, Any, Optional
from ..config import (
    PITTSBURGH_LAT,
    PITTSBURGH_LNG,
    SEARCH_RADIUS_METERS,
    OVERPASS_DELAY,
    OVERPASS_URL,
    OVERPASS_AMENITIES,
    OVERPASS_SHOPS
)

# Office tags for professional services
OVERPASS_OFFICES = []

logger = logging.getLogger(__name__)


class OverpassAPICollector:
    """Collector for business data from Overpass API (OpenStreetMap)."""
    
    def __init__(self, base_url: str = OVERPASS_URL):
        """Initialize the Overpass API collector.
        
        Args:
            base_url: Overpass API endpoint URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'BusinessDataCollector/1.0 (Educational Purpose)'
        })
        
    def collect_businesses(self, 
                          lat: float = PITTSBURGH_LAT,
                          lng: float = PITTSBURGH_LNG,
                          radius: int = SEARCH_RADIUS_METERS) -> List[Dict[str, Any]]:
        """Collect business data from Overpass API.
        
        Args:
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters
            
        Returns:
            List of business data dictionaries
        """
        businesses = []
        
        # Collect amenities
        logger.info("Collecting amenities from Overpass API...")
        amenity_businesses = self._collect_amenities(lat, lng, radius)
        businesses.extend(amenity_businesses)
        
        time.sleep(OVERPASS_DELAY)
        
        # Collect shops
        logger.info("Collecting shops from Overpass API...")
        shop_businesses = self._collect_shops(lat, lng, radius)
        businesses.extend(shop_businesses)

        time.sleep(OVERPASS_DELAY)

        # Collect offices
        logger.info("Collecting offices from Overpass API...")
        office_businesses = self._collect_offices(lat, lng, radius)
        businesses.extend(office_businesses)

        logger.info(f"Collected {len(businesses)} businesses from Overpass API")
        return businesses

    def _get_filters(self) -> tuple:
        """Get current amenities, shops, and offices filters"""
        return OVERPASS_AMENITIES.copy(), OVERPASS_SHOPS.copy(), OVERPASS_OFFICES.copy()

    def _set_filters(self, amenities: List[str], shops: List[str], offices: List[str] = None):
        """Temporarily set amenities, shops, and offices filters for collection"""
        global OVERPASS_AMENITIES, OVERPASS_SHOPS, OVERPASS_OFFICES
        OVERPASS_AMENITIES[:] = amenities
        OVERPASS_SHOPS[:] = shops
        if offices is not None:
            OVERPASS_OFFICES[:] = offices
    
    def _collect_amenities(self, lat: float, lng: float, radius: int) -> List[Dict[str, Any]]:
        """Collect amenity businesses from Overpass API.
        
        Args:
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters
            
        Returns:
            List of business data dictionaries
        """
        amenity_filter = '|'.join(OVERPASS_AMENITIES)
        
        query = f"""
        [out:json][timeout:25];
        (
          node["amenity"~"^({amenity_filter})$"]["name"](around:{radius},{lat},{lng});
          way["amenity"~"^({amenity_filter})$"]["name"](around:{radius},{lat},{lng});
          relation["amenity"~"^({amenity_filter})$"]["name"](around:{radius},{lat},{lng});
        );
        out center meta;
        """
        
        return self._execute_query(query, 'amenity')
    
    def _collect_shops(self, lat: float, lng: float, radius: int) -> List[Dict[str, Any]]:
        """Collect shop businesses from Overpass API.
        
        Args:
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters
            
        Returns:
            List of business data dictionaries
        """
        shop_filter = '|'.join(OVERPASS_SHOPS)
        
        query = f"""
        [out:json][timeout:25];
        (
          node["shop"~"^({shop_filter})$"]["name"](around:{radius},{lat},{lng});
          way["shop"~"^({shop_filter})$"]["name"](around:{radius},{lat},{lng});
          relation["shop"~"^({shop_filter})$"]["name"](around:{radius},{lat},{lng});
        );
        out center meta;
        """
        
        return self._execute_query(query, 'shop')

    def _collect_offices(self, lat: float, lng: float, radius: int) -> List[Dict[str, Any]]:
        """Collect office businesses from Overpass API.

        Args:
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters

        Returns:
            List of business data dictionaries
        """
        if not OVERPASS_OFFICES:
            return []

        office_filter = '|'.join(OVERPASS_OFFICES)

        query = f"""
        [out:json][timeout:25];
        (
          node["office"~"^({office_filter})$"]["name"](around:{radius},{lat},{lng});
          way["office"~"^({office_filter})$"]["name"](around:{radius},{lat},{lng});
          relation["office"~"^({office_filter})$"]["name"](around:{radius},{lat},{lng});
        );
        out center meta;
        """

        return self._execute_query(query, 'office')

    def _execute_query(self, query: str, category: str) -> List[Dict[str, Any]]:
        """Execute Overpass QL query and parse results.
        
        Args:
            query: Overpass QL query string
            category: Category of businesses being queried
            
        Returns:
            List of business data dictionaries
        """
        try:
            response = self.session.post(
                self.base_url,
                data=query,
                timeout=30
            )
            response.raise_for_status()
            
            data = response.json()
            elements = data.get('elements', [])
            
            businesses = []
            for element in elements:
                business = self._parse_element_data(element, category)
                if business:
                    businesses.append(business)
                    
            return businesses
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for {category}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error for {category}: {str(e)}")
            return []
    
    def _parse_element_data(self, element: Dict[str, Any], category: str) -> Optional[Dict[str, Any]]:
        """Parse element data from Overpass API response.
        
        Args:
            element: Element data from API response
            category: Category of the business (amenity/shop)
            
        Returns:
            Parsed business data dictionary or None if invalid
        """
        try:
            tags = element.get('tags', {})
            
            # Extract basic information
            name = tags.get('name', '').strip()
            if not name:
                return None
                
            # Extract contact information
            phone = (tags.get('phone') or tags.get('contact:phone', '')).strip()
            website = (tags.get('website') or
                      tags.get('contact:website') or
                      tags.get('url', '')).strip()

            # Extract social media information
            facebook = (tags.get('contact:facebook') or
                       tags.get('facebook', '')).strip()
            instagram = (tags.get('contact:instagram') or
                        tags.get('instagram', '')).strip()
            twitter = (tags.get('contact:twitter') or
                      tags.get('twitter', '')).strip()
            linkedin = (tags.get('contact:linkedin') or
                       tags.get('linkedin', '')).strip()
            yelp = tags.get('yelp', '').strip()
            
            # Extract address information
            street = tags.get('addr:street', '')
            house_number = tags.get('addr:housenumber', '')
            city = tags.get('addr:city', '')
            postcode = tags.get('addr:postcode', '')
            
            # Build address
            address_parts = []
            if house_number and street:
                address_parts.append(f"{house_number} {street}")
            elif street:
                address_parts.append(street)
            if city:
                address_parts.append(city)
            if postcode:
                address_parts.append(postcode)
            
            address = ', '.join(address_parts)
            
            # Extract location
            if element['type'] == 'node':
                latitude = element.get('lat')
                longitude = element.get('lon')
            else:
                # For ways and relations, use center coordinates
                center = element.get('center', {})
                latitude = center.get('lat')
                longitude = center.get('lon')
            
            # Extract business type
            business_type = tags.get(category, '')
            
            # Extract additional information
            opening_hours = tags.get('opening_hours', '')
            email = tags.get('email') or tags.get('contact:email', '')
            
            business_data = {
                'name': name,
                'address': address,
                'phone': phone,
                'website': website,
                'facebook': facebook,
                'instagram': instagram,
                'twitter': twitter,
                'linkedin': linkedin,
                'yelp': yelp,
                'types': [business_type] if business_type else [],
                'primary_type': business_type,
                'business_status': 'OPERATIONAL',  # Assume operational if in OSM
                'latitude': latitude,
                'longitude': longitude,
                'opening_hours': opening_hours,
                'email': email,
                'category': category,
                'source': 'overpass_api'
            }
            
            return business_data
            
        except Exception as e:
            logger.error(f"Error parsing element data: {str(e)}")
            return None
