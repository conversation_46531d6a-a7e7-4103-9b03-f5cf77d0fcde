# Business Data Collector CLI

A comprehensive command-line interface for collecting business data and scraping contact information from multiple sources.

## 🚀 Features

- **Multi-source Data Collection**: Collect from Google Places API, Overpass API (OpenStreetMap), and Yellow Pages
- **Unified Category System**: User-friendly business categories that map to all data sources
- **Smart Location Services**: Geocoding support for city names, coordinates, and addresses
- **Email & Contact Scraping**: Integrated email and social media scraping from business websites
- **Interactive & Quick Modes**: Both guided interactive collection and fast command-line collection
- **Configuration Management**: Save and manage your preferences
- **Results Management**: View, analyze, and manage collection results

## 📦 Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up the CLI (optional):
```bash
pip install -e .
```

## 🎯 Quick Start

### Interactive Collection (Recommended for beginners)
```bash
python -m cli.main collect interactive
```

This will guide you through:
1. Selecting business categories
2. Setting search location
3. Configuring search radius
4. Choosing data sources
5. Setting output options

### Quick Collection (For experienced users)
```bash
python -m cli.main collect quick -c restaurants -l "Pittsburgh, PA" -r 10
```

### Email Scraping
```bash
python -m cli.main scrape emails output/businesses.csv -a 20 -b 5 -c 2
```

## 📋 Available Commands

### Collection Commands

#### `collect interactive`
Guided interactive business data collection with step-by-step prompts.

#### `collect quick`
Quick collection with command-line arguments:
- `-c, --category`: Business categories (can be used multiple times)
- `-l, --location`: City or location to search in
- `-r, --radius`: Search radius in miles (default: 10.0)
- `-s, --source`: Data sources to use (google, overpass, yellowpages)
- `-o, --output`: Output file path

**Example:**
```bash
python -m cli.main collect quick -c restaurants -c cafes_coffee -l "New York, NY" -r 15 -s google -s overpass
```

### Category Management

#### `categories list`
List all available business categories:
```bash
python -m cli.main categories list
python -m cli.main categories list --group "Food & Dining"
python -m cli.main categories list --detailed
```

#### `categories search`
Search for specific categories:
```bash
python -m cli.main categories search restaurant
python -m cli.main categories search auto
```

#### `categories info`
Get detailed information about a category:
```bash
python -m cli.main categories info restaurants
```

#### `categories groups`
List all category groups:
```bash
python -m cli.main categories groups
```

### Email & Contact Scraping

#### `scrape emails`
Scrape emails from business websites:
```bash
python -m cli.main scrape emails input.csv -o scraped_emails.csv -a 50 -b 10 -c 3
```

Options:
- `-o, --output`: Output file for scraped emails
- `-a, --amount`: Limit number of URLs to process
- `-b, --batch-size`: URLs per batch (default: 25)
- `-c, --concurrent`: Concurrent requests per batch (default: 5)
- `--website-col`: Column name containing website URLs (default: "website")

#### `scrape contacts`
Scrape all contact information (emails + social media):
```bash
python -m cli.main scrape contacts input.csv -o scraped_contacts.csv -a 30
```

### Configuration Management

#### `config show`
Display current configuration:
```bash
python -m cli.main config show
```

#### `config set`
Set configuration values:
```bash
python -m cli.main config set default_radius 15
python -m cli.main config set default_sources "google,overpass"
python -m cli.main config set google_api_key "your-api-key"
```

#### `config reset`
Reset configuration to defaults:
```bash
python -m cli.main config reset
```

### Results Management

#### `results list`
List all collection results:
```bash
python -m cli.main results list
```

#### `results show`
Show details of a specific result file:
```bash
python -m cli.main results show businesses_20250801_123456.csv
```

#### `results clean`
Clean up old result files:
```bash
python -m cli.main results clean
```

## 🏷️ Business Categories

The CLI uses a unified category system that maps to all data sources:

### Food & Dining
- `restaurants` - Full-service restaurants and dining establishments
- `cafes_coffee` - Coffee shops, cafes, and casual dining spots
- `bars_pubs` - Bars, pubs, and drinking establishments
- `fast_food` - Quick service restaurants and fast food chains

### Retail & Shopping
- `retail_stores` - General retail and merchandise stores
- `clothing_fashion` - Clothing stores, fashion boutiques, and apparel shops
- `electronics` - Electronics stores, computer shops, and tech retailers
- `grocery_supermarket` - Grocery stores, supermarkets, and food retailers

### Health & Medical
- `medical_services` - Doctors, clinics, and medical practices
- `dentists` - Dental offices and oral health services
- `pharmacies` - Pharmacies and drug stores
- `veterinary` - Veterinarians and animal hospitals

### Automotive
- `auto_repair` - Auto repair shops and automotive services
- `gas_stations` - Fuel stations and gas stations
- `car_wash` - Car wash and auto detailing services

### Professional Services
- `legal_services` - Lawyers, law firms, and legal services
- `real_estate` - Real estate agencies and property services
- `accounting_finance` - Accountants, financial advisors, and tax services

### Beauty & Wellness
- `beauty_salons` - Hair salons, beauty parlors, and cosmetic services
- `spas_wellness` - Spas, massage therapy, and wellness centers
- `gyms_fitness` - Gyms, fitness centers, and exercise facilities

## 🌍 Location Formats

The CLI supports multiple location input formats:

- **City names**: `"Pittsburgh"`, `"New York City"`
- **City, State**: `"Pittsburgh, PA"`, `"Los Angeles, CA"`
- **Coordinates**: `"40.4406,-79.9959"` or `"40.4406, -79.9959"`

## 📊 Data Sources

### Google Places API
- Most comprehensive and reliable
- Requires API key
- Returns structured business data including websites
- Rate limited to prevent abuse

### Overpass API (OpenStreetMap)
- Free and open source
- Good coverage of local businesses
- No API key required
- Queries for amenities and shops

### Yellow Pages
- Web scraping approach with LLM extraction
- Additional business coverage
- Respectful scraping with delays
- Requires OpenAI API key for LLM processing

## 📁 Output Formats

Results are exported in multiple formats:

- **CSV**: Structured data for analysis and import
- **JSON**: Machine-readable format for integration
- **Summary Report**: Human-readable summary with statistics

## ⚙️ Configuration

Configuration is stored in `~/.business-cli/config.json` and includes:

- Default search radius
- Preferred data sources
- API keys
- Scraping batch sizes
- Output preferences

## 🔧 Advanced Usage

### Batch Processing
```bash
# Collect multiple categories
python -m cli.main collect quick -c restaurants -c cafes_coffee -c bars_pubs -l "Seattle, WA"

# Chain collection and scraping
python -m cli.main collect quick -c restaurants -l "Boston, MA" -o restaurants.csv
python -m cli.main scrape emails restaurants.csv -a 100 -o restaurant_emails.csv
```

### Custom Workflows
```bash
# Large-scale collection with specific sources
python -m cli.main collect quick -c retail_stores -l "Chicago, IL" -r 25 -s google -s overpass

# High-volume email scraping
python -m cli.main scrape emails large_dataset.csv -b 50 -c 10 -a 1000
```

## 🚨 Rate Limiting & Best Practices

- **Google Places**: 1 request per second (configurable)
- **Overpass API**: 0.5 seconds between requests
- **Yellow Pages**: 2 seconds between requests
- **Email Scraping**: Configurable batch size and concurrency

### Recommended Settings
- For testing: `--amount 20 --batch-size 5 --concurrent 2`
- For production: `--batch-size 25 --concurrent 5`
- For large datasets: `--batch-size 50 --concurrent 10`

## 🔑 API Keys

Set your API keys using the config command:

```bash
python -m cli.main config set google_api_key "your-google-places-api-key"
python -m cli.main config set openai_api_key "your-openai-api-key"
```

Or set them as environment variables:
```bash
export GOOGLE_PLACES_API_KEY="your-key"
export OPENAI_API_KEY="your-key"
```

## 📈 Performance Tips

1. **Use appropriate batch sizes**: Start small (5-10) for testing, scale up for production
2. **Limit concurrent requests**: Too many can trigger rate limits
3. **Use the amount parameter**: Test with small datasets first
4. **Choose relevant sources**: Not all sources work well for all categories
5. **Monitor rate limits**: Watch for 429 errors and adjust delays

## 🐛 Troubleshooting

### Common Issues

**"Could not find location"**
- Try different location formats
- Use coordinates if city name fails
- Check spelling and include state/country

**"No businesses found"**
- Try different categories or broader search radius
- Check if the location has businesses of that type
- Verify API keys are set correctly

**"Email scraping functionality not available"**
- Ensure the `aganl` module is in the correct location
- Check that all dependencies are installed
- Verify the import paths are correct

**Rate limiting errors**
- Reduce batch size and concurrent requests
- Increase delays between requests
- Check API quotas and limits

## 📞 Support

For issues and questions:
1. Check this documentation
2. Use `--help` with any command for detailed options
3. Review the configuration with `config show`
4. Check results with `results list` and `results show`

## 🎉 Success Stories

The CLI has been successfully used to:
- Collect 1,236 restaurants in Pittsburgh with 50% email success rate
- Process multiple data sources simultaneously
- Export results in multiple formats for analysis
- Manage large-scale data collection projects
