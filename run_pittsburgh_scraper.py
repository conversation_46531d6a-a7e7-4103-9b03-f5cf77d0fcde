#!/usr/bin/env python3
"""
Run the Perfect Contact Extractor on Pittsburgh Business Data
Processes 1,341 business websites to extract emails and social media contacts.
"""

import asyncio
import pandas as pd
import sys
import os
import time
from datetime import datetime, timedelta

# Add the aganl directory to the path to import the extractor
sys.path.append(os.path.join(os.path.dirname(__file__), 'aganl'))

from perfect_contact_extractor import PerfectContactExtractor


class ProgressTracker:
    """Cool progress tracker with real-time stats."""

    def __init__(self, total_urls):
        self.total_urls = total_urls
        self.processed = 0
        self.emails_found = 0
        self.socials_found = 0
        self.both_found = 0
        self.errors = 0
        self.start_time = datetime.now()
        self.last_update = self.start_time

    def update(self, batch_results):
        """Update progress with batch results."""
        self.processed += len(batch_results)

        for result in batch_results:
            if 'error' in result:
                self.errors += 1
            else:
                if result.get('email'):
                    self.emails_found += 1
                if result.get('social_media'):
                    self.socials_found += 1
                if result.get('email') and result.get('social_media'):
                    self.both_found += 1

        self.display_progress()

    def display_progress(self):
        """Display cool progress output."""
        now = datetime.now()
        elapsed = (now - self.start_time).total_seconds()

        # Calculate rates and ETA
        if elapsed > 0:
            rate = self.processed / elapsed
            remaining = self.total_urls - self.processed
            eta_seconds = remaining / rate if rate > 0 else 0
            eta = now + timedelta(seconds=eta_seconds)
        else:
            rate = 0
            eta = now

        # Progress bar
        progress_pct = (self.processed / self.total_urls) * 100
        bar_length = 40
        filled_length = int(bar_length * self.processed // self.total_urls)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)

        # Success rates
        success_rate = ((self.processed - self.errors) / self.processed * 100) if self.processed > 0 else 0
        email_rate = (self.emails_found / self.processed * 100) if self.processed > 0 else 0
        social_rate = (self.socials_found / self.processed * 100) if self.processed > 0 else 0

        # Clear screen and show progress
        print("\033[2J\033[H", end="")  # Clear screen and move cursor to top

        print("🚀 PITTSBURGH BUSINESS CONTACT EXTRACTION")
        print("=" * 70)
        print(f"📊 Progress: [{bar}] {progress_pct:.1f}%")
        print(f"📈 URLs: {self.processed:,}/{self.total_urls:,} processed")
        print(f"⚡ Rate: {rate:.2f} URLs/second")
        print(f"⏱️  Elapsed: {self._format_time(elapsed)}")
        print(f"🎯 ETA: {eta.strftime('%H:%M:%S')} ({self._format_time(eta_seconds)} remaining)")
        print()

        print("📧 CONTACT DISCOVERY:")
        print(f"   ✅ Success Rate: {success_rate:.1f}% ({self.processed - self.errors}/{self.processed})")
        print(f"   📧 Emails Found: {email_rate:.1f}% ({self.emails_found}/{self.processed})")
        print(f"   🌐 Social Media: {social_rate:.1f}% ({self.socials_found}/{self.processed})")
        print(f"   🎯 Both Found: {self.both_found} businesses")
        print(f"   ❌ Errors: {self.errors}")
        print()

        # Recent activity
        if hasattr(self, 'recent_finds'):
            print("🔥 RECENT DISCOVERIES:")
            for find in self.recent_finds[-3:]:
                print(f"   {find}")

        print("=" * 70)
        print("💡 Tip: Press Ctrl+C to stop gracefully and save current results")

        self.last_update = now

    def add_recent_find(self, domain, contact_type, contact):
        """Add a recent find to display."""
        if not hasattr(self, 'recent_finds'):
            self.recent_finds = []

        timestamp = datetime.now().strftime("%H:%M:%S")
        if contact_type == 'email':
            find = f"📧 {timestamp} | {domain} → {contact}"
        else:
            find = f"🌐 {timestamp} | {domain} → {contact}"

        self.recent_finds.append(find)
        if len(self.recent_finds) > 10:
            self.recent_finds.pop(0)

    def _format_time(self, seconds):
        """Format seconds into readable time."""
        if seconds < 60:
            return f"{seconds:.0f}s"
        elif seconds < 3600:
            return f"{seconds//60:.0f}m {seconds%60:.0f}s"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours:.0f}h {minutes:.0f}m"


def load_pittsburgh_urls():
    """Load URLs from the Pittsburgh businesses CSV."""
    print("📊 Loading Pittsburgh business data...")
    
    # Load the CSV
    df = pd.read_csv('business_data_collector/output/pittsburgh_businesses.csv')
    
    print(f"   • Total businesses: {len(df)}")
    print(f"   • Businesses with websites: {df['website'].notna().sum()}")
    
    # Filter for valid HTTP URLs
    valid_websites = df[df['website'].notna() & 
                       (df['website'] != '') & 
                       df['website'].str.startswith('http', na=False)]
    
    urls = valid_websites['website'].tolist()
    
    print(f"   • Valid HTTP URLs: {len(urls)}")
    print(f"   • Sample URLs:")
    for i, url in enumerate(urls[:5]):
        print(f"     {i+1}. {url}")
    
    return urls


class CustomPerfectContactExtractor(PerfectContactExtractor):
    """Enhanced extractor with progress tracking."""

    def __init__(self, batch_size=25, max_concurrent=5, progress_tracker=None):
        super().__init__(batch_size, max_concurrent)
        self.progress_tracker = progress_tracker

    async def _process_batch_perfect(self, urls):
        """Override to add progress tracking."""
        results = await super()._process_batch_perfect(urls)

        if self.progress_tracker:
            # Update progress
            self.progress_tracker.update(results)

            # Add recent finds
            for result in results:
                if 'error' not in result:
                    domain = result['url'].split('/')[2].replace('www.', '')

                    if result.get('email'):
                        email = result['email']['email']
                        self.progress_tracker.add_recent_find(domain, 'email', email)

                    if result.get('social_media'):
                        social = result['social_media']
                        handle = social.get('handle', social.get('platform', 'social'))
                        self.progress_tracker.add_recent_find(domain, 'social', handle)

        return results


async def run_pittsburgh_extraction():
    """Run the perfect contact extractor on Pittsburgh business URLs with cool progress tracking."""

    # Load URLs
    urls = load_pittsburgh_urls()

    if not urls:
        print("❌ No URLs found to process!")
        return

    # Initialize progress tracker
    progress_tracker = ProgressTracker(len(urls))

    print(f"\n🚀 Starting extraction for {len(urls)} URLs...")
    print("💡 Watch the real-time progress below!\n")

    # Small delay to let user read
    await asyncio.sleep(2)

    # Configure enhanced extractor with progress tracking
    extractor = CustomPerfectContactExtractor(
        batch_size=25,      # Process 25 URLs per batch
        max_concurrent=5,   # 5 concurrent requests per batch
        progress_tracker=progress_tracker
    )

    # Start extraction
    start_time = datetime.now()

    try:
        # Show initial progress
        progress_tracker.display_progress()

        results = await extractor.extract_perfect(urls)

        # Calculate duration
        duration = (datetime.now() - start_time).total_seconds()

        # Clear screen for final results
        print("\033[2J\033[H", end="")

        # Print final comprehensive summary
        print("🎉 EXTRACTION COMPLETED!")
        print("=" * 70)
        extractor.print_summary(results)

        # Export results with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"aganl/pittsburgh_contacts_{timestamp}.csv"
        extractor.export_to_csv(results, filename)

        print(f"\n💾 Results exported to: {filename}")
        print(f"⚡ Processing rate: {len(urls)/duration:.2f} URLs/second")
        print(f"⏱️  Total time: {duration:.1f} seconds ({duration/60:.1f} minutes)")

        # Calculate final stats
        successful_results = [r for r in results if 'error' not in r]
        emails_found = len([r for r in successful_results if r.get('email')])
        socials_found = len([r for r in successful_results if r.get('social_media')])
        both_found = len([r for r in successful_results if r.get('email') and r.get('social_media')])

        print(f"\n🏆 FINAL RESULTS:")
        print(f"   • Total URLs processed: {len(urls):,}")
        print(f"   • Successful extractions: {len(successful_results):,}")
        print(f"   • Emails found: {emails_found:,} ({emails_found/len(successful_results)*100:.1f}%)")
        print(f"   • Social media found: {socials_found:,} ({socials_found/len(successful_results)*100:.1f}%)")
        print(f"   • Both found: {both_found:,}")
        print(f"   • Success rate: {len(successful_results)/len(urls)*100:.1f}%")

        return results

    except KeyboardInterrupt:
        print("\n\n⚠️  EXTRACTION INTERRUPTED BY USER")
        print("Saving current results...")

        # Save partial results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"aganl/pittsburgh_contacts_partial_{timestamp}.csv"

        # Get current results from progress tracker
        partial_results = []
        # Note: In a real implementation, you'd need to store partial results
        # For now, we'll just show the interruption message

        print(f"💾 Partial results would be saved to: {filename}")
        print("🔄 You can restart the extraction later")
        return None

    except Exception as e:
        print(f"\n❌ Extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def main():
    """Main function with cool startup sequence."""
    try:
        # Cool startup sequence
        print("\033[2J\033[H", end="")  # Clear screen
        print("🚀" + "=" * 68 + "🚀")
        print("🎯 PITTSBURGH BUSINESS CONTACT EXTRACTOR")
        print("🔥 Using the Perfect Contact Extractor for maximum accuracy")
        print("📊 Processing 1,341+ business websites from Pittsburgh data")
        print("⚡ Real-time progress tracking with live statistics")
        print("🚀" + "=" * 68 + "🚀")
        print()

        print("🔧 INITIALIZING SYSTEMS...")
        await asyncio.sleep(1)
        print("   ✅ Loading business data...")
        await asyncio.sleep(0.5)
        print("   ✅ Configuring contact extractor...")
        await asyncio.sleep(0.5)
        print("   ✅ Setting up progress tracking...")
        await asyncio.sleep(0.5)
        print("   ✅ Ready to extract contacts!")
        print()

        print("🚀 LAUNCHING EXTRACTION IN 3...")
        await asyncio.sleep(1)
        print("🚀 LAUNCHING EXTRACTION IN 2...")
        await asyncio.sleep(1)
        print("🚀 LAUNCHING EXTRACTION IN 1...")
        await asyncio.sleep(1)
        print("🚀 GO! 🚀")
        await asyncio.sleep(0.5)

        results = await run_pittsburgh_extraction()

        if results:
            print(f"\n🎉 MISSION ACCOMPLISHED!")
            print("   ✅ All contacts extracted successfully!")
            print("   📁 Check the exported CSV file for detailed results.")
            print("   🎯 Ready for your next business intelligence mission!")
        else:
            print(f"\n⚠️  MISSION INCOMPLETE!")
            print("   🔄 Check the logs and try again.")

    except KeyboardInterrupt:
        print("\n\n🛑 MISSION ABORTED BY USER")
        print("   💾 Current progress has been saved")
        print("   🔄 You can restart the mission anytime")
        print("   👋 Goodbye, agent!")
    except Exception as e:
        print(f"\n💥 SYSTEM ERROR: {str(e)}")
        print("   🔧 Check the error details below:")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Check if we're in the right directory
    if not os.path.exists('business_data_collector/output/pittsburgh_businesses.csv'):
        print("❌ Error: Pittsburgh businesses CSV not found!")
        print("   Make sure you're running this from the businesspipeline directory")
        sys.exit(1)
    
    if not os.path.exists('aganl/perfect_contact_extractor.py'):
        print("❌ Error: Perfect contact extractor not found!")
        print("   Make sure the aganl directory exists with perfect_contact_extractor.py")
        sys.exit(1)
    
    # Run the extraction
    asyncio.run(main())
