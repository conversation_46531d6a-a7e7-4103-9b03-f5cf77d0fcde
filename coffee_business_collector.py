"""
Coffee Business Collector - Find businesses that would want morning coffee service
Uses all 3 data sources to collect thousands of office-based business contacts
"""
import logging
import sys
import os
from datetime import datetime
from typing import List, Dict, Any, Tuple
import pandas as pd

# Add the business_data_collector directory to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'business_data_collector'))

from collectors.google_places import GooglePlacesCollector
from collectors.overpass_api import OverpassAPICollector
from collectors.yellow_pages import YellowPagesCollector
from utils.deduplication import BusinessDeduplicator
from utils.filtering import BusinessFilter
from utils.data_export import DataExporter
from config import GROQ_API_KEY

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('coffee_business_collection.log')
    ]
)

logger = logging.getLogger(__name__)

# Major metropolitan areas to search for maximum coverage
MAJOR_CITIES = [
    {"name": "Pittsburgh, PA", "lat": 40.440624, "lng": -79.995888},
    {"name": "New York, NY", "lat": 40.7128, "lng": -74.0060},
    {"name": "Los Angeles, CA", "lat": 34.0522, "lng": -118.2437},
    {"name": "Chicago, IL", "lat": 41.8781, "lng": -87.6298},
    {"name": "Houston, TX", "lat": 29.7604, "lng": -95.3698},
    {"name": "Philadelphia, PA", "lat": 39.9526, "lng": -75.1652},
    {"name": "Phoenix, AZ", "lat": 33.4484, "lng": -112.0740},
    {"name": "San Antonio, TX", "lat": 29.4241, "lng": -98.4936},
    {"name": "San Diego, CA", "lat": 32.7157, "lng": -117.1611},
    {"name": "Dallas, TX", "lat": 32.7767, "lng": -96.7970}
]

# Coffee-focused business categories - office environments with employees
COFFEE_BUSINESS_CATEGORIES = {
    # Professional Services (High Priority)
    "law_firms": {
        "google_types": ["lawyer"],
        "overpass_offices": ["lawyer"],
        "yellow_pages_terms": ["law firms", "attorneys", "lawyers", "legal services"]
    },
    "accounting_firms": {
        "google_types": ["accounting"],
        "overpass_offices": ["accountant", "tax", "tax_advisor"],
        "yellow_pages_terms": ["accounting firms", "accountants", "tax services", "bookkeeping", "CPA"]
    },
    "insurance_agencies": {
        "google_types": ["insurance_agency"],
        "overpass_offices": ["insurance"],
        "yellow_pages_terms": ["insurance agencies", "insurance companies", "insurance brokers"]
    },
    "real_estate_agencies": {
        "google_types": ["real_estate_agency"],
        "overpass_offices": ["real_estate_agent", "estate_agent"],
        "yellow_pages_terms": ["real estate agencies", "realtors", "property management"]
    },
    "financial_advisors": {
        "google_types": ["accounting"],  # Closest match
        "overpass_offices": ["financial", "financial_advisor"],
        "yellow_pages_terms": ["financial advisors", "investment firms", "wealth management", "financial planning"]
    },
    
    # Medical/Health Services (High Priority)
    "medical_services": {
        "google_types": ["doctor", "hospital"],
        "overpass_amenities": ["hospital", "clinic", "doctors"],
        "yellow_pages_terms": ["medical services", "doctors", "healthcare", "clinics"]
    },
    "dentists": {
        "google_types": ["dentist"],
        "overpass_amenities": ["dentist"],
        "yellow_pages_terms": ["dentists", "dental services", "dental offices"]
    },
    "veterinary": {
        "google_types": ["veterinary_care"],
        "overpass_amenities": ["veterinary"],
        "yellow_pages_terms": ["veterinary", "animal hospitals", "vet clinics"]
    },
    
    # Financial Services
    "banks": {
        "google_types": ["bank"],
        "overpass_amenities": ["bank"],
        "yellow_pages_terms": ["banks", "credit unions", "banking services"]
    },
    
    # Government/Public Services
    "government_offices": {
        "google_types": ["government_office", "city_hall", "local_government_office"],
        "overpass_amenities": ["townhall"],
        "overpass_offices": ["government"],
        "yellow_pages_terms": ["government offices", "city hall", "public services"]
    },
    
    # Education
    "schools": {
        "google_types": ["school", "primary_school", "secondary_school"],
        "overpass_amenities": ["school"],
        "yellow_pages_terms": ["schools", "elementary schools", "high schools", "private schools"]
    },
    "universities": {
        "google_types": ["university"],
        "overpass_amenities": ["university", "college"],
        "yellow_pages_terms": ["universities", "colleges", "higher education"]
    },
    
    # Additional Business Types (Not in existing categories)
    "marketing_agencies": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["marketing agencies", "advertising agencies", "digital marketing", "marketing firms"]
    },
    "it_companies": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["IT companies", "software companies", "technology companies", "computer services"]
    },
    "consulting_firms": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["consulting firms", "business consultants", "management consulting"]
    },
    "architecture_firms": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["architecture firms", "architects", "architectural services"]
    },
    "engineering_firms": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["engineering firms", "engineers", "engineering services"]
    },
    "nonprofits": {
        "google_types": ["establishment"],
        "yellow_pages_terms": ["non-profit organizations", "nonprofit", "charitable organizations"]
    }
}

class CoffeeBusinessCollector:
    """Specialized collector for businesses that would want coffee service"""
    
    def __init__(self):
        self.google_collector = GooglePlacesCollector()
        self.overpass_collector = OverpassAPICollector()
        self.yellow_pages_collector = YellowPagesCollector(
            llm_provider="groq/llama-3.1-8b-instant",
            api_key=GROQ_API_KEY
        )
        self.deduplicator = BusinessDeduplicator()
        self.filter = BusinessFilter()
        self.exporter = DataExporter()
        
    def collect_coffee_businesses(self, max_cities: int = 5) -> List[Dict[str, Any]]:
        """
        Collect businesses from multiple cities that would want coffee service
        
        Args:
            max_cities: Maximum number of cities to search (default 5 for faster execution)
        """
        logger.info("=" * 60)
        logger.info("COFFEE BUSINESS COLLECTION STARTED")
        logger.info("=" * 60)
        
        all_businesses = []
        cities_to_search = MAJOR_CITIES[:max_cities]
        
        for city_idx, city in enumerate(cities_to_search, 1):
            logger.info(f"\n--- Collecting from {city['name']} ({city_idx}/{len(cities_to_search)}) ---")
            
            city_businesses = self._collect_from_city(
                city['name'], city['lat'], city['lng']
            )
            
            # Add city information to each business
            for business in city_businesses:
                business['search_city'] = city['name']
                business['search_coordinates'] = f"{city['lat']}, {city['lng']}"
            
            all_businesses.extend(city_businesses)
            logger.info(f"Collected {len(city_businesses)} businesses from {city['name']}")
        
        logger.info(f"\nTotal businesses collected from all cities: {len(all_businesses)}")
        
        # Process and deduplicate
        processed_businesses = self._process_businesses(all_businesses)
        
        # Export results
        self._export_results(processed_businesses)
        
        return processed_businesses
    
    def _collect_from_city(self, city_name: str, lat: float, lng: float) -> List[Dict[str, Any]]:
        """Collect businesses from a single city using all 3 data sources"""
        city_businesses = []
        
        # Collect from Google Places API
        logger.info(f"Collecting from Google Places API for {city_name}...")
        google_businesses = self._collect_google_places(lat, lng)
        city_businesses.extend(google_businesses)
        
        # Collect from Overpass API  
        logger.info(f"Collecting from Overpass API for {city_name}...")
        overpass_businesses = self._collect_overpass_api(lat, lng)
        city_businesses.extend(overpass_businesses)
        
        # Collect from Yellow Pages
        logger.info(f"Collecting from Yellow Pages for {city_name}...")
        yellow_pages_businesses = self._collect_yellow_pages(city_name)
        city_businesses.extend(yellow_pages_businesses)
        
        return city_businesses
    
    def _collect_google_places(self, lat: float, lng: float) -> List[Dict[str, Any]]:
        """Collect from Google Places API for coffee-focused business types"""
        businesses = []
        
        for category_name, category_config in COFFEE_BUSINESS_CATEGORIES.items():
            if 'google_types' not in category_config:
                continue
                
            logger.info(f"  Searching Google Places for {category_name}...")
            
            try:
                category_businesses = self.google_collector.collect_businesses(
                    lat=lat,
                    lng=lng,
                    business_types=category_config['google_types'],
                    max_results=20  # Limit per category to manage API usage
                )
                
                # Add category information
                for business in category_businesses:
                    business['coffee_category'] = category_name
                
                businesses.extend(category_businesses)
                logger.info(f"    Found {len(category_businesses)} {category_name}")
                
            except Exception as e:
                logger.error(f"    Error collecting {category_name} from Google Places: {str(e)}")
        
        return businesses
    
    def _collect_overpass_api(self, lat: float, lng: float) -> List[Dict[str, Any]]:
        """Collect from Overpass API for coffee-focused business types"""
        # The existing Overpass collector handles multiple amenity types
        # We'll use it as-is and then filter for our coffee-focused categories
        try:
            businesses = self.overpass_collector.collect_businesses(lat=lat, lng=lng)
            
            # Filter for coffee-relevant business types
            coffee_businesses = []
            for business in businesses:
                business_type = business.get('type', '').lower()
                amenity = business.get('amenity', '').lower()
                
                # Check if this business type is relevant for coffee service
                if any([
                    'office' in business_type,
                    'bank' in amenity,
                    'hospital' in amenity,
                    'clinic' in amenity,
                    'dentist' in amenity,
                    'veterinary' in amenity,
                    'school' in amenity,
                    'university' in amenity,
                    'government' in business_type
                ]):
                    business['coffee_category'] = 'office_building'
                    coffee_businesses.append(business)
            
            return coffee_businesses
            
        except Exception as e:
            logger.error(f"Error collecting from Overpass API: {str(e)}")
            return []
    
    def _collect_yellow_pages(self, city_name: str) -> List[Dict[str, Any]]:
        """Collect from Yellow Pages for coffee-focused business types"""
        businesses = []

        for category_name, category_config in COFFEE_BUSINESS_CATEGORIES.items():
            if 'yellow_pages_terms' not in category_config:
                continue

            logger.info(f"  Searching Yellow Pages for {category_name}...")

            try:
                # Search each term individually for better results
                for term in category_config['yellow_pages_terms']:
                    term_businesses = self.yellow_pages_collector.collect_businesses_by_term(
                        search_term=term,
                        location=city_name,
                        max_results=10  # Limit per term to manage execution time
                    )

                    # Add category information
                    for business in term_businesses:
                        business['coffee_category'] = category_name
                        business['search_term'] = term

                    businesses.extend(term_businesses)
                    logger.info(f"    Found {len(term_businesses)} for '{term}'")

            except Exception as e:
                logger.error(f"    Error collecting {category_name} from Yellow Pages: {str(e)}")

        return businesses
    
    def _process_businesses(self, businesses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process collected business data (filter and deduplicate)"""
        logger.info(f"\nProcessing {len(businesses)} collected businesses...")
        
        if not businesses:
            logger.warning("No businesses to process")
            return []
        
        # Add collection timestamp
        timestamp = datetime.now().isoformat()
        for business in businesses:
            business['collected_at'] = timestamp
            business['collection_purpose'] = 'coffee_service_prospects'
        
        # Filter out corporate chains and invalid businesses
        logger.info("Filtering businesses...")
        filtered_businesses = self.filter.filter_businesses(businesses)
        
        # Deduplicate businesses
        logger.info("Deduplicating businesses...")
        unique_businesses = self.deduplicator.deduplicate_businesses(filtered_businesses)
        
        logger.info(f"Processing complete: {len(unique_businesses)} unique coffee prospect businesses")
        return unique_businesses
    
    def _export_results(self, businesses: List[Dict[str, Any]]) -> None:
        """Export processed business data to CSV"""
        logger.info("Exporting results...")
        
        if not businesses:
            logger.warning("No businesses to export")
            return
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"coffee_business_prospects_{timestamp}.csv"
        
        # Export to CSV
        csv_path = self.exporter.export_to_csv(businesses, filename)
        if csv_path:
            logger.info(f"CSV exported to: {csv_path}")
            print(f"\n✅ Coffee business prospects exported to: {csv_path}")
        
        # Export summary report
        report_path = self.exporter.export_summary_report(businesses)
        if report_path:
            logger.info(f"Summary report exported to: {report_path}")
        
        # Print summary statistics
        self._print_summary_stats(businesses)
    
    def _print_summary_stats(self, businesses: List[Dict[str, Any]]) -> None:
        """Print summary statistics about collected businesses"""
        print(f"\n📊 COFFEE BUSINESS COLLECTION SUMMARY")
        print(f"=" * 50)
        print(f"Total unique businesses: {len(businesses)}")
        
        # Breakdown by category
        categories = {}
        cities = {}
        sources = {}
        
        for business in businesses:
            # Category breakdown
            category = business.get('coffee_category', 'unknown')
            categories[category] = categories.get(category, 0) + 1
            
            # City breakdown
            city = business.get('search_city', 'unknown')
            cities[city] = cities.get(city, 0) + 1
            
            # Source breakdown
            source = business.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        print(f"\nBreakdown by business category:")
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"  {category}: {count}")
        
        print(f"\nBreakdown by city:")
        for city, count in sorted(cities.items(), key=lambda x: x[1], reverse=True):
            print(f"  {city}: {count}")
        
        print(f"\nBreakdown by data source:")
        for source, count in sorted(sources.items(), key=lambda x: x[1], reverse=True):
            print(f"  {source}: {count}")


def main():
    """Main function to run the coffee business collection"""
    try:
        collector = CoffeeBusinessCollector()
        
        # Collect from 5 major cities to get thousands of contacts
        businesses = collector.collect_coffee_businesses(max_cities=5)
        
        print(f"\n🎉 Collection completed successfully!")
        print(f"📈 Found {len(businesses)} unique coffee business prospects")
        
    except KeyboardInterrupt:
        logger.info("Collection interrupted by user")
        print("\n❌ Collection interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error during collection: {str(e)}")
        print(f"\n❌ Error during collection: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
